import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import md5 from "md5";
import { logger } from "../utils/logger";

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
    }
  }
}

/**
 * API Logger middleware
 * Logs incoming requests and outgoing responses
 */
const apiLogger = (req: Request, res: Response, next: NextFunction) => {
  // Generate unique request ID
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Log incoming request
  logger.info(`Incoming ${req.method} ${req.originalUrl}`, {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get("User-Agent"),
    ip: req.ip,
    headers: {
      "x-provider": req.get("x-provider"),
      "zactcompanyid": req.get("zactcompanyid"),
      "content-type": req.get("content-type"),
    },
    bodyHash: req.body ? md5(JSON.stringify(req.body)) : undefined,
  });

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function (body: any) {
    const duration = Date.now() - (req.startTime || 0);
    
    logger.info(`Outgoing ${req.method} ${req.originalUrl}`, {
      requestId: req.requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: JSON.stringify(body).length,
    });

    return originalJson.call(this, body);
  };

  next();
};

export default apiLogger;
