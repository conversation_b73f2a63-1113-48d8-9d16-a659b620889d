import { AuthProvider, ProviderType, ProviderFactory } from "../interfaces/provider";
import { QboProvider } from "./qbo-provider";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";

/**
 * Factory class for creating authentication providers
 * Implements the Factory pattern for extensible provider management
 */
export class AuthProviderFactory implements ProviderFactory {
  private static instance: AuthProviderFactory;
  private providers: Map<ProviderType, () => AuthProvider> = new Map();

  private constructor() {
    this.registerProviders();
  }

  /**
   * Singleton pattern implementation
   */
  public static getInstance(): AuthProviderFactory {
    if (!AuthProviderFactory.instance) {
      AuthProviderFactory.instance = new AuthProviderFactory();
    }
    return AuthProviderFactory.instance;
  }

  /**
   * Register all available providers
   */
  private registerProviders(): void {
    // Register QBO provider
    this.providers.set("qbo", () => new QboProvider());
    
    // Future providers will be registered here:
    // this.providers.set("xero", () => new XeroProvider());
    // this.providers.set("netsuite", () => new NetSuiteProvider());
    // this.providers.set("qbd", () => new QbdProvider());
  }

  /**
   * Create a provider instance based on type
   */
  public createProvider(type: ProviderType): AuthProvider {
    const providerFactory = this.providers.get(type);
    
    if (!providerFactory) {
      throw new ApiException({
        ...ErrorCodes.UNPROCESSABLE_ENTITY,
        errorDescription: `Unsupported provider type: ${type}. Supported providers: ${this.getSupportedProviders().join(", ")}`,
      });
    }

    return providerFactory();
  }

  /**
   * Get list of supported provider types
   */
  public getSupportedProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if a provider type is supported
   */
  public isProviderSupported(type: string): type is ProviderType {
    return this.providers.has(type as ProviderType);
  }

  /**
   * Register a new provider (for future extensibility)
   */
  public registerProvider(type: ProviderType, factory: () => AuthProvider): void {
    this.providers.set(type, factory);
  }
}
