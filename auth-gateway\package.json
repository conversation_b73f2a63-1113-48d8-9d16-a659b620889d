{"name": "qbo-auth-gateway", "version": "1.0.0", "description": "QBO-focused Auth Gateway Microservice for secure authentication middleware", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["auth", "qbo", "quickbooks", "o<PERSON>h", "microservice"], "author": "Zact Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "axios": "^1.5.0", "express-validator": "^7.0.1", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "uuid": "^9.0.0", "md5": "^2.3.0", "prom-client": "^14.2.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/uuid": "^9.0.2", "@types/md5": "^2.3.2", "@types/node": "^20.5.0", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "rimraf": "^5.0.1"}, "engines": {"node": ">=18.0.0"}}