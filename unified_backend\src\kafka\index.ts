import { KAFKA_TOPICS } from "./../constants/kafkaTopics";
import logger from "../utils/logger";
import baseConsumer from "./consumers/base-consumer";
import { initializeEntityCreateConsumer } from "./consumers/entity-create-consumer";
import * as accountingPlatformProducer from "./producers/accounting-platform-producer";
import baseProducer from "./producers/base-producer";

/**
 * Initialize all Kafka producers and consumers
 */
export const initializeKafka = async (): Promise<void> => {
  try {
    // Initialize producer first
    await baseProducer.initialize();

    // Initialize consumers
    await initializeEntityCreateConsumer();

    // Log producer topics
    const producerTopics = [
      KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
      KAFKA_TOPICS.ENTITY_BATCH_STREAM,
      KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
    ];

    logger.info(`📤 Producer ready for topics: ${producerTopics.join(", ")}`);
    logger.info("✅ Kafka connected - Producer and Consumer ready");
  } catch (error) {
    logger.error(`❌ Failed to initialize Kafka: ${error}`);
    throw error;
  }
};

/**
 * Gracefully shut down Kafka connections
 */
export const shutdownKafka = async (): Promise<void> => {
  try {
    logger.info("Shutting down Kafka connections...");

    // Disconnect producer and consumer
    await baseProducer.disconnect();
    await baseConsumer.disconnect();

    logger.info("Kafka connections shut down successfully");
  } catch (error) {
    logger.error(`Error shutting down Kafka connections: ${error}`);
  }
};

// Export all Kafka components
export { accountingPlatformProducer, baseConsumer, baseProducer };
