import crypto from "crypto";
import { Request } from "express";
import { logSecurityEvent } from "../middlewares/audit-logger";

/**
 * Security utilities for the auth gateway
 */

/**
 * Generate a secure random state parameter
 */
export function generateSecureState(orgId: string): string {
  const timestamp = Date.now().toString();
  const random = crypto.randomBytes(16).toString("hex");
  const data = JSON.stringify({ orgId, timestamp, random });
  return Buffer.from(data).toString("base64url");
}

/**
 * Validate and parse state parameter
 */
export function validateAndParseState(state: string): { orgId: string; timestamp: number } {
  try {
    const decoded = Buffer.from(state, "base64url").toString("utf8");
    const parsed = JSON.parse(decoded);
    
    if (!parsed.orgId || !parsed.timestamp) {
      throw new Error("Invalid state structure");
    }

    // Check if state is not too old (5 minutes max)
    const maxAge = 5 * 60 * 1000; // 5 minutes
    if (Date.now() - parsed.timestamp > maxAge) {
      throw new Error("State parameter expired");
    }

    return {
      orgId: parsed.orgId,
      timestamp: parsed.timestamp,
    };
  } catch (error) {
    throw new Error("Invalid state parameter");
  }
}

/**
 * Sanitize string input to prevent injection attacks
 */
export function sanitizeString(input: string): string {
  if (typeof input !== "string") {
    return "";
  }

  return input
    .trim()
    .replace(/[<>\"'&]/g, "") // Remove potentially dangerous characters
    .substring(0, 1000); // Limit length
}

/**
 * Validate request origin for CSRF protection
 */
export function validateRequestOrigin(req: Request, allowedOrigins: string[]): boolean {
  const origin = req.get("Origin") || req.get("Referer");
  
  if (!origin) {
    return false; // No origin header
  }

  try {
    const originUrl = new URL(origin);
    return allowedOrigins.some(allowed => {
      const allowedUrl = new URL(allowed);
      return originUrl.origin === allowedUrl.origin;
    });
  } catch {
    return false; // Invalid origin URL
  }
}

/**
 * Rate limiting key generator
 */
export function generateRateLimitKey(req: Request): string {
  const ip = req.ip;
  const provider = req.get("x-provider") || "unknown";
  const orgId = req.get("zactcompanyid") || "unknown";
  
  return `${ip}:${provider}:${orgId}`;
}

/**
 * Detect suspicious activity patterns
 */
export function detectSuspiciousActivity(req: Request): boolean {
  const suspiciousPatterns = [
    // Check for SQL injection patterns
    /(\b(union|select|insert|update|delete|drop|create|alter)\b)/i,
    // Check for script injection patterns
    /<script|javascript:|vbscript:|onload|onerror/i,
    // Check for path traversal
    /\.\.\//,
    // Check for command injection
    /[;&|`$()]/,
  ];

  const checkString = JSON.stringify(req.body) + req.originalUrl;
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(checkString)) {
      logSecurityEvent("SUSPICIOUS_PATTERN_DETECTED", {
        pattern: pattern.toString(),
        url: req.originalUrl,
        body: req.body,
      }, req, "warn");
      return true;
    }
  }

  return false;
}

/**
 * Hash sensitive data for logging
 */
export function hashForLogging(data: string): string {
  return crypto.createHash("sha256").update(data).digest("hex").substring(0, 8);
}

/**
 * Validate connection ID format
 */
export function validateConnectionId(connectionId: string): boolean {
  // UUID v4 format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(connectionId);
}

/**
 * Validate realm ID format (QBO specific)
 */
export function validateRealmId(realmId: string): boolean {
  // QBO realm IDs are typically numeric strings
  return /^\d+$/.test(realmId) && realmId.length >= 10 && realmId.length <= 15;
}

/**
 * Validate authorization code format
 */
export function validateAuthCode(code: string): boolean {
  // Basic validation for auth codes
  return typeof code === "string" && 
         code.length >= 10 && 
         code.length <= 500 && 
         /^[a-zA-Z0-9._-]+$/.test(code);
}
