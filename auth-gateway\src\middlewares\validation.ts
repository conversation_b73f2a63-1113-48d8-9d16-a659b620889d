import { body, header, ValidationChain } from "express-validator";

/**
 * Validation middleware for auth requests
 */

// Common header validations
export const validateProviderHeader: ValidationChain = header("x-provider")
  .notEmpty()
  .withMessage("x-provider header is required")
  .isIn(["qbo", "xero", "netsuite", "qbd"])
  .withMessage(
    "Invalid provider. Supported providers: qbo, xero, netsuite, qbd"
  );

export const validateOrgIdHeader: ValidationChain = header("zactcompanyid")
  .notEmpty()
  .withMessage("zactcompanyid header is required")
  .isString()
  .withMessage("zactcompanyid must be a string");

// Auth action validation
export const validateAuthAction: ValidationChain = body("action")
  .notEmpty()
  .withMessage("action is required")
  .isIn(["initiate", "exchange", "disconnect"])
  .withMessage(
    "Invalid action. Supported actions: initiate, exchange, disconnect"
  );

// Initiate auth validation
export const validateInitiateAuth = [
  validateProviderHeader,
  validateOrgIdHeader,
  validateAuthAction,
];

// Exchange token validation
export const validateExchangeToken = [
  validateProviderHeader,
  validateAuthAction,
  body("code")
    .notEmpty()
    .withMessage("code is required for exchange action")
    .isString()
    .withMessage("code must be a string"),
  body("realmId")
    .notEmpty()
    .withMessage("realmId is required for exchange action")
    .isString()
    .withMessage("realmId must be a string"),
  body("state")
    .notEmpty()
    .withMessage("state is required for exchange action")
    .isString()
    .withMessage("state must be a string"),
];

// Disconnect validation
export const validateDisconnect = [
  validateProviderHeader,
  validateAuthAction,
  body("connectionId")
    .notEmpty()
    .withMessage("connectionId is required for disconnect action")
    .isString()
    .withMessage("connectionId must be a string"),
];

// Dynamic validation based on action
export const validateAuthRequest = [
  validateProviderHeader,
  validateAuthAction,
  // Conditional validations will be handled in the route handler
];

// Additional security validations
export const validateRequestSize = body().custom((value, { req }) => {
  const bodySize = JSON.stringify(req.body).length;
  if (bodySize > 10000) {
    // 10KB limit
    throw new Error("Request body too large");
  }
  return true;
});

export const validateNoScriptInjection = [
  body("*").escape().trim(),
  header("*").escape().trim(),
];

// Sanitize input to prevent XSS
export const sanitizeInput = [
  body("code").optional().isString().trim().escape(),
  body("realmId").optional().isString().trim().escape(),
  body("state").optional().isString().trim().escape(),
  body("connectionId").optional().isString().trim().escape(),
  header("zactcompanyid").optional().isString().trim().escape(),
];
