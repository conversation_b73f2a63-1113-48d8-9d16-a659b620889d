/**
 * Auth-related interfaces and types
 */

export interface AuthUrlResponse {
  data: string;
  message: string;
}

export interface TokenResponse {
  message: string;
  data: {
    connectionId: string;
    realmId: string;
    erpSystem: string;
  };
}

export interface RevokeResponse {
  message: string;
  data: {
    connectionId: string;
    erpSystem: string;
  };
}

export interface AuthRequest {
  action: "initiate" | "exchange" | "refresh" | "disconnect";
  orgId?: string;
  code?: string;
  realmId?: string;
  state?: string;
  connectionId?: string;
}

export interface QboTokenData {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface QboCompanyInfo {
  CompanyInfo: {
    CompanyName: string;
  };
}

export interface UbTokenRequest {
  orgId: string;
  realmId: string;
  companyName: string;
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  erpSystem: string;
}

export interface UbTokenResponse {
  connectionId: string;
  message: string;
}

export interface UbDisconnectRequest {
  connectionId: string;
  erpSystem: string;
}

export interface UbDisconnectResponse {
  message: string;
}
