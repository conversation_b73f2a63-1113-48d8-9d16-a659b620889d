#!/bin/bash

# Health Check Script for All Services

echo "🏥 Zact Platform Health Check"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "Checking $service_name... "
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✅ Healthy${NC}"
        return 0
    else
        echo -e "${RED}❌ Unhealthy${NC}"
        return 1
    fi
}

# Function to check Docker service
check_docker_service() {
    local service_name=$1
    
    echo -n "Checking Docker service $service_name... "
    
    if docker-compose ps | grep -q "$service_name.*Up"; then
        echo -e "${GREEN}✅ Running${NC}"
        return 0
    else
        echo -e "${RED}❌ Not Running${NC}"
        return 1
    fi
}

echo ""
echo "🐳 Docker Services:"
check_docker_service "auth-gateway"
check_docker_service "unified-backend"
check_docker_service "mysql"
check_docker_service "kafka"
check_docker_service "zookeeper"

echo ""
echo "🌐 HTTP Health Checks:"
check_service "Auth Gateway" "http://localhost:3001/health"
check_service "Unified Backend" "http://localhost:8080/health"
check_service "Kafka UI" "http://localhost:8090"

echo ""
echo "💾 Database Connectivity:"
echo -n "Checking MySQL connection... "
if docker exec zact-mysql mysqladmin ping -h localhost -u root -p$(grep MYSQL_ROOT_PASSWORD .env | cut -d '=' -f2) 2>/dev/null | grep -q "mysqld is alive"; then
    echo -e "${GREEN}✅ Connected${NC}"
else
    echo -e "${RED}❌ Connection Failed${NC}"
fi

echo ""
echo "📨 Kafka Connectivity:"
echo -n "Checking Kafka brokers... "
if docker exec zact-kafka kafka-broker-api-versions --bootstrap-server localhost:9092 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Connected${NC}"
else
    echo -e "${RED}❌ Connection Failed${NC}"
fi

echo ""
echo "🔗 Service Communication:"
echo -n "Auth Gateway → Unified Backend... "
if curl -s -o /dev/null -w "%{http_code}" "http://localhost:3001/health" | grep -q "200"; then
    echo -e "${GREEN}✅ Communication OK${NC}"
else
    echo -e "${YELLOW}⚠️ Check logs${NC}"
fi

echo ""
echo "================================"
echo "Health check complete!"
