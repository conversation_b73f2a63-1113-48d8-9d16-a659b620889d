import { Request, Response, NextFunction } from "express";
import { ErrorCodes } from "./response";
import { logger } from "./logger";

/**
 * Helper function to check if status code is valid
 */
export const isInvalidStatus = (status?: number) => {
  return !status || status < 100 || status > 599;
};

/**
 * Global error handler middleware
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (res.headersSent) return;

  if (error.message?.includes("invalid input syntax")) {
    error = {
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: error.message,
    };
  }

  // Token-related errors
  if (
    ["invalid token", "jwt malformed"].includes(error.message) ||
    error.name === "JsonWebTokenError"
  ) {
    error.status = 401;
    error.message = "Invalid token format. Please provide a valid JWT";
  }

  if (error.name === "TokenExpiredError") {
    error.status = 401;
    error.message = "Your session has timed out. Please login again";
  }

  if (isInvalidStatus(error.status)) {
    error.status = 500;
  }

  logger.error(`${req.method} ${req.originalUrl}:`, error);
  
  // Preserve error code and structure for 500 errors
  const status = error.status || 500;
  const isServerError = status === 500;
 
  const response = {
    error: isServerError
      ? {
          status: status,
          code: error?.code || 105,
          errorDescription: error?.errorDescription || error?.message,
        }
      : error,
    message: isServerError
      ? "Something went wrong"
      : error?.message || error?.errorDescription,
    responseStatus: status,
  };

  res.status(status).json(response);
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const error: any = new Error("Path not found");
  error.status = 404;
  next(error); // Forward to global error handler
};
