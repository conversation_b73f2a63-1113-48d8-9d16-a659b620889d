import { BaseAuthProvider } from "./base-provider";
import {
  AuthUrlResponse,
  TokenResponse,
  RevokeResponse,
  QboTokenData,
  QboCompanyInfo,
} from "../interfaces/auth";
import { envConfig } from "../config/config";
import { axiosInstance } from "../utils/api-utils";
import ApiException from "../utils/api-exception";
import { ErrorCodes, HttpStatus, ErrorCode } from "../utils/response";
import logger from "../utils/logger";
import { UbService } from "../services/ub-service";

/**
 * QuickBooks Online authentication provider
 */
export class QboProvider extends BaseAuthProvider {
  private ubService: UbService;

  constructor() {
    super("QBO");
    this.ubService = new UbService();
  }

  /**
   * Generate QBO OAuth authorization URL
   */
  async generateAuthUrl(orgId: string): Promise<AuthUrlResponse> {
    try {
      this.validateRequired(orgId, "orgId");

      const { clientId, redirectUri, authUrl } = envConfig.qbo;

      const scopes = [
        "com.intuit.quickbooks.accounting",
        "openid",
        "profile",
        "email",
      ].join(" ");

      const params = new URLSearchParams({
        client_id: clientId,
        response_type: "code",
        scope: scopes,
        redirect_uri: redirectUri,
        state: orgId, // Using orgId as state
      });

      const generatedAuthUrl = `${authUrl}?${params.toString()}`;

      logger.info(`Generated QBO auth URL for orgId: ${orgId}`);

      return {
        data: generatedAuthUrl,
        message: "QBO authorization URL generated successfully",
      };
    } catch (error) {
      logger.error("Error generating QBO auth URL:", error);
      this.handleProviderError(error, "auth URL generation");
    }
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(
    code: string,
    realmId: string,
    state: string
  ): Promise<TokenResponse> {
    try {
      this.validateRequired(code, "code");
      this.validateRequired(realmId, "realmId");
      this.validateRequired(state, "state");

      const orgId = state; // orgId was passed as state
      const { clientId, clientSecret, redirectUri, tokenUrl, baseUrl } =
        envConfig.qbo;

      // Validate QBO configuration
      if (!clientId || !clientSecret || !redirectUri || !tokenUrl || !baseUrl) {
        throw new ApiException({
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: "QBO configuration is incomplete",
        });
      }

      // Step 1: Exchange code for tokens
      const tokenData = await this.exchangeCodeForTokens(
        code,
        clientId,
        clientSecret,
        redirectUri,
        tokenUrl
      );

      // Step 2: Fetch company information
      const companyName = await this.fetchCompanyInfo(
        tokenData.access_token,
        realmId,
        baseUrl
      );

      // Step 3: Send tokens to Unified Backend
      const ubResponse = await this.ubService.storeTokens({
        orgId,
        realmId,
        companyName,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresAt: new Date(
          Date.now() + tokenData.expires_in * 1000
        ).toISOString(),
        erpSystem: "qbo",
      });

      logger.info(
        `Successfully exchanged QBO tokens for orgId: ${orgId}, realmId: ${realmId}`
      );

      return {
        message: "Successfully connected to QBO and tokens stored",
        data: {
          connectionId: ubResponse.connectionId,
          realmId: realmId,
          erpSystem: "qbo",
        },
      };
    } catch (error) {
      logger.error("Error exchanging QBO tokens:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      throw new ApiException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.INTERNAL_ERROR,
        message: "Failed to process QBO token exchange",
      });
    }
  }

  /**
   * Refresh QBO access token using refresh token
   */
  async refreshToken(connectionId: string): Promise<TokenResponse> {
    try {
      this.validateRequired(connectionId, "connectionId");

      // Step 1: Get current tokens from Unified Backend
      const currentTokenData = await this.ubService.getTokens(connectionId);

      // Step 2: Refresh tokens with QBO
      const newTokenData = await this.refreshQboTokens(
        currentTokenData.refreshToken,
        envConfig.qbo.clientId,
        envConfig.qbo.clientSecret,
        envConfig.qbo.tokenUrl
      );

      // Step 3: Update tokens in Unified Backend
      const ubResponse = await this.ubService.updateTokens({
        connectionId,
        accessToken: newTokenData.access_token,
        refreshToken: newTokenData.refresh_token,
        expiresAt: new Date(
          Date.now() + newTokenData.expires_in * 1000
        ).toISOString(),
      });

      logger.info(
        `Successfully refreshed QBO tokens for connectionId: ${connectionId}`
      );

      return {
        message: "Successfully refreshed QBO tokens",
        data: {
          connectionId,
          realmId: currentTokenData.realmId,
          erpSystem: "qbo",
        },
      };
    } catch (error) {
      logger.error("Error refreshing QBO tokens:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      throw new ApiException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.INTERNAL_ERROR,
        message: "Failed to refresh QBO tokens",
      });
    }
  }

  /**
   * Revoke QBO access token and disconnect
   */
  async revokeToken(connectionId: string): Promise<RevokeResponse> {
    try {
      this.validateRequired(connectionId, "connectionId");

      // Step 1: Get token from Unified Backend
      const tokenData = await this.ubService.getTokens(connectionId);

      // Step 2: Revoke token with QBO
      await this.revokeQboToken(tokenData.accessToken, tokenData.refreshToken);

      // Step 3: Delete connection from Unified Backend
      await this.ubService.deleteConnection(connectionId);

      logger.info(
        `Successfully revoked QBO tokens for connectionId: ${connectionId}`
      );

      return {
        message: "Successfully disconnected from QBO",
        data: {
          connectionId,
          erpSystem: "qbo",
        },
      };
    } catch (error) {
      logger.error("Error revoking QBO tokens:", error);
      this.handleProviderError(error, "token revocation");
    }
  }

  /**
   * Private helper methods
   */
  private async exchangeCodeForTokens(
    code: string,
    clientId: string,
    clientSecret: string,
    redirectUri: string,
    tokenUrl: string
  ): Promise<QboTokenData> {
    const params = new URLSearchParams({
      grant_type: "authorization_code",
      code,
      redirect_uri: redirectUri,
    });

    const response = await axiosInstance.post(tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${clientId}:${clientSecret}`
        ).toString("base64")}`,
      },
    });

    if (!response.data.access_token || !response.data.refresh_token) {
      throw new Error("Missing token data in QBO response");
    }

    return {
      access_token: response.data.access_token,
      refresh_token: response.data.refresh_token,
      expires_in: response.data.expires_in,
    };
  }

  private async fetchCompanyInfo(
    accessToken: string,
    realmId: string,
    baseUrl: string
  ): Promise<string> {
    const response = await axiosInstance.get<QboCompanyInfo>(
      `${baseUrl}/${realmId}/companyinfo/${realmId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
      }
    );

    return response.data.CompanyInfo.CompanyName;
  }

  private async refreshQboTokens(
    refreshToken: string,
    clientId: string,
    clientSecret: string,
    tokenUrl: string
  ): Promise<QboTokenData> {
    const params = new URLSearchParams({
      grant_type: "refresh_token",
      refresh_token: refreshToken,
    });

    const response = await axiosInstance.post(tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${clientId}:${clientSecret}`
        ).toString("base64")}`,
      },
    });

    if (!response.data.access_token || !response.data.refresh_token) {
      throw new Error("Missing token data in QBO refresh response");
    }

    return response.data;
  }

  private async revokeQboToken(
    accessToken: string,
    refreshToken: string
  ): Promise<void> {
    const { revokeUrl, clientId, clientSecret } = envConfig.qbo;

    const params = new URLSearchParams({
      token: refreshToken,
    });

    await axiosInstance.post(revokeUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${clientId}:${clientSecret}`
        ).toString("base64")}`,
      },
    });
  }
}
