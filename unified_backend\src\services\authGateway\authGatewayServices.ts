import { ConnectionStatus, SyncOperationType } from "@prisma/client";
import prisma from "../../config/db";
import ApiException from "../../utils/api-exception";
import logger from "../../utils/logger";
import { ErrorCode, HttpStatus } from "../../utils/response";
import { startQboBackgroundSync } from "../../utils/qbo-utils";
import baseProducer from "../../kafka/producers/base-producer";
import { KAFKA_TOPICS } from "../../constants/kafkaTopics";
import { v4 as uuidv4 } from "uuid";
import {
  MessageSource,
  MessageDestination,
  MessageType,
  ERPSystem,
} from "../../interfaces/kafkaMessageInterface";

/**
 * Interface for storing tokens from Auth Gateway
 */
interface StoreTokensRequest {
  orgId: string;
  realmId: string;
  companyName: string;
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  erpSystem: string;
}

/**
 * Interface for token response to Auth Gateway
 */
interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  realmId: string;
}

/**
 * Store authentication tokens from Auth Gateway
 */
const storeTokens = async (tokenRequest: StoreTokensRequest) => {
  try {
    const {
      orgId,
      realmId,
      companyName,
      accessToken,
      refreshToken,
      expiresAt,
      erpSystem,
    } = tokenRequest;

    logger.info(
      `Storing tokens from Auth Gateway for orgId: ${orgId}, realmId: ${realmId}`
    );

    // Create authentication data
    const authData = {
      accessToken,
      refreshToken,
      expiresAt,
      lastRefreshedAt: new Date().toISOString(),
    };

    // Check for existing integrations
    const existingIntegration =
      await prisma.accountingPlatformIntegration.findFirst({
        where: {
          zactCompanyId: orgId,
          externalCompanyId: realmId,
          accountingPlatformType: erpSystem.toUpperCase(),
        },
      });

    let integration;

    if (existingIntegration) {
      // Handle existing integration
      if (existingIntegration.connectionStatus === ConnectionStatus.INACTIVE) {
        // Reactivate inactive integration
        integration = await prisma.accountingPlatformIntegration.update({
          where: { id: existingIntegration.id },
          data: {
            connectionStatus: ConnectionStatus.ACTIVE,
            lastConnectedAt: new Date(),
            authentication: authData,
            companyName,
          },
        });

        logger.info(`Reactivated existing integration: ${integration.id}`);
      } else {
        // Update active integration
        integration = await prisma.accountingPlatformIntegration.update({
          where: { id: existingIntegration.id },
          data: {
            authentication: authData,
            companyName,
            lastConnectedAt: new Date(),
          },
        });

        logger.info(`Updated existing integration: ${integration.id}`);
      }
    } else {
      // Create new integration
      integration = await prisma.accountingPlatformIntegration.create({
        data: {
          zactCompanyId: orgId,
          externalCompanyId: realmId,
          accountingPlatformType: erpSystem.toUpperCase(),
          connectionStatus: ConnectionStatus.ACTIVE,
          authentication: authData,
          companyName,
          lastConnectedAt: new Date(),
        },
      });

      logger.info(`Created new integration: ${integration.id}`);
    }

    // Send sync started message to Kafka
    await sendSyncStartedMessage(integration, SyncOperationType.FULL_SYNC);

    // Start background sync
    startQboBackgroundSync(
      integration.id,
      accessToken,
      realmId,
      SyncOperationType.FULL_SYNC
    );

    return {
      message: "Tokens stored successfully",
      data: {
        connectionId: integration.id,
        erpSystem: erpSystem,
        realmId: realmId,
      },
    };
  } catch (error: any) {
    logger.error("Error storing tokens from Auth Gateway:", error);

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to store authentication tokens",
    });
  }
};

/**
 * Get authentication tokens for Auth Gateway
 */
const getTokens = async (
  connectionId: string
): Promise<{ message: string; data: TokenResponse }> => {
  try {
    logger.info(`Getting tokens for connectionId: ${connectionId}`);

    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: connectionId },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: "Connection not found",
      });
    }

    if (integration.connectionStatus !== ConnectionStatus.ACTIVE) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        message: "Connection is not active",
      });
    }

    const auth = integration.authentication as any;

    if (!auth || !auth.accessToken || !auth.refreshToken) {
      throw new ApiException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.INTERNAL_ERROR,
        message: "Authentication data is incomplete",
      });
    }

    return {
      message: "Tokens retrieved successfully",
      data: {
        accessToken: auth.accessToken,
        refreshToken: auth.refreshToken,
        realmId: integration.externalCompanyId,
      },
    };
  } catch (error: any) {
    logger.error("Error getting tokens for Auth Gateway:", error);

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to retrieve authentication tokens",
    });
  }
};

/**
 * Update authentication tokens for Auth Gateway
 */
const updateTokens = async (updateRequest: {
  connectionId: string;
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
}) => {
  try {
    logger.info(
      `Updating tokens for connectionId: ${updateRequest.connectionId}`
    );

    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: updateRequest.connectionId },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: "Connection not found",
      });
    }

    // Update authentication data
    const authData = {
      accessToken: updateRequest.accessToken,
      refreshToken: updateRequest.refreshToken,
      expiresAt: updateRequest.expiresAt,
      lastRefreshedAt: new Date().toISOString(),
    };

    await prisma.accountingPlatformIntegration.update({
      where: { id: updateRequest.connectionId },
      data: {
        authentication: authData,
      },
    });

    logger.info(
      `Successfully updated tokens for connectionId: ${updateRequest.connectionId}`
    );

    return {
      message: "Tokens updated successfully",
    };
  } catch (error: any) {
    logger.error("Error updating tokens for Auth Gateway:", error);

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to update authentication tokens",
    });
  }
};

/**
 * Delete connection for Auth Gateway
 */
const deleteConnection = async (connectionId: string) => {
  try {
    logger.info(`Deleting connection for connectionId: ${connectionId}`);

    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: connectionId },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: "Connection not found",
      });
    }

    // Update connection status to INACTIVE instead of deleting
    await prisma.accountingPlatformIntegration.update({
      where: { id: connectionId },
      data: {
        connectionStatus: ConnectionStatus.INACTIVE,
      },
    });

    logger.info(`Successfully disconnected integration: ${connectionId}`);

    return {
      message: "Connection deleted successfully",
    };
  } catch (error: any) {
    logger.error("Error deleting connection for Auth Gateway:", error);

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to delete connection",
    });
  }
};

/**
 * Send sync started message to Kafka
 */
const sendSyncStartedMessage = async (
  integration: any,
  syncType: SyncOperationType
) => {
  try {
    const message = {
      messageId: uuidv4(),
      correlationId: uuidv4(),
      messageType: MessageType.EVENT,
      source: MessageSource.UNIFIED_BACKEND,
      destination: MessageDestination.ZACT_APP,
      timestamp: new Date().toISOString(),
      securityContext: {
        connectionId: integration.id,
        zactCompanyId: integration.zactCompanyId,
      },
      payload: {
        syncOperation: syncType,
        erpSystem: ERPSystem.QBO,
        status: "STARTED",
        message: `${syncType} sync started for QBO connection`,
      },
    };

    await baseProducer.sendMessage(
      KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
      message,
      integration.id
    );

    logger.info(`Sent sync started message for integration: ${integration.id}`);
  } catch (error) {
    logger.error("Error sending sync started message:", error);
    // Don't throw error here as it's not critical for the auth flow
  }
};

export { storeTokens, getTokens, updateTokens, deleteConnection };
