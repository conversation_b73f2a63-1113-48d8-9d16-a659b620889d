# =============================================================================
# Zact Unified Platform - Environment Configuration
# =============================================================================

# =============================================================================
# SERVICE PORTS
# =============================================================================
AUTH_GATEWAY_PORT=3001
UNIFIED_BACKEND_PORT=8080
MYSQL_PORT=3306
MYSQL_DEV_PORT=3307
KAFKA_PORT=9092
KAFKA_DEV_PORT=9093
KAFKA_UI_PORT=8090
KAFKA_UI_DEV_PORT=8091

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Production Database
DATABASE_URL=mysql://zact_user:zact_password@mysql:3306/zact_unified
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_DATABASE=zact_unified
MYSQL_USER=zact_user
MYSQL_PASSWORD=zact_password

# Development Database
MYSQL_DEV_ROOT_PASSWORD=devpassword
MYSQL_DEV_DATABASE=zact_unified_dev
MYSQL_DEV_USER=zact_dev
MYSQL_DEV_PASSWORD=devpassword

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
KAFKA_CLIENT_ID=zact-unified-backend
KAFKA_BROKERS=kafka:29092

# =============================================================================
# QBO (QUICKBOOKS ONLINE) CONFIGURATION
# =============================================================================
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_REDIRECT_URI=http://localhost:4200/auth/callback
QBO_ENVIRONMENT=sandbox
QBO_AUTH_URL=https://appcenter.intuit.com/connect/oauth2
QBO_TOKEN_URL=https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
QBO_REVOKE_URL=https://developer.api.intuit.com/v2/oauth2/tokens/revoke
QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com/v3/company
QBO_API_BASE_URL=https://quickbooks.api.intuit.com/v3/company

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your_jwt_secret_key_here
UB_API_KEY=your_unified_backend_api_key
AUTH_GATEWAY_API_KEY=your_auth_gateway_api_key

# =============================================================================
# API GATEWAY CONFIGURATION
# =============================================================================
API_GATEWAY_URL=http://localhost:8080

# =============================================================================
# CORS AND SECURITY
# =============================================================================
CORS_ORIGIN=http://localhost:4200
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=10m
LOG_MAX_FILES=7

# =============================================================================
# FEATURE FLAGS
# =============================================================================
INCREMENTAL_SYNC_CRON_ENABLED=false

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================
# Uncomment for development
# NODE_ENV=development
# LOG_LEVEL=debug
# RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# Uncomment for production
# NODE_ENV=production
# LOG_LEVEL=warn
# RATE_LIMIT_MAX_REQUESTS=100
