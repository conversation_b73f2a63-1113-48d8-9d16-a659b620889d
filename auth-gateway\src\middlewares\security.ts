import rateLimit from "express-rate-limit";
import { envConfig } from "../config/config";

/**
 * Rate limiting middleware
 */
export const rateLimiter = rateLimit({
  windowMs: envConfig.security.rateLimitWindowMs, // 15 minutes
  max: envConfig.security.rateLimitMaxRequests, // limit each IP to 100 requests per windowMs
  message: {
    error: {
      status: 429,
      code: 109,
      errorDescription: "Too many requests from this IP, please try again later",
    },
    message: "Rate limit exceeded",
    responseStatus: 429,
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

/**
 * CORS configuration
 */
export const corsOptions = {
  origin: envConfig.security.corsOrigin,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "x-provider",
    "zactcompanyid",
    "X-Requested-With",
  ],
  credentials: true,
  optionsSuccessStatus: 200,
};
