import * as cron from "node-cron";
import prisma from "../../config/db";
import { SyncedEntityType } from "@prisma/client";
import {
  processEntityDataWithDetection,
  updateEntitySyncState,
} from "../../utils/syncUtils";
import { sendScheduledSyncData } from "../kafka-message";
import { fetchAllDataWithPlanning } from "../../services/api-gateway";
import { createSyncInfoMessage } from "../../utils/kafka-messageUtils";
import baseProducer from "../../kafka/producers/base-producer";
import { getValidQboToken } from "../../middlewares/tokenUtils";
import logger from "../../utils/logger";
import { envConfig } from "../../config/config";

// Configuration from environment variables
const CRON_CONFIG = {
  ENABLED: envConfig.cron.incrementalSync.enabled,
  SCHEDULE: envConfig.cron.incrementalSync.schedule,
  BATCH_SIZE: envConfig.cron.incrementalSync.batchSize,
  ENTITY_TYPES: envConfig.cron.incrementalSync
    .entityTypes as SyncedEntityType[],
  MAX_CONCURRENT: envConfig.cron.incrementalSync.maxConcurrent,
  RETRY_ATTEMPTS: envConfig.cron.incrementalSync.retryAttempts,
  RETRY_DELAY: envConfig.cron.incrementalSync.retryDelay,
  TIMEZONE: envConfig.cron.incrementalSync.timezone,
};

interface CronSyncResult {
  zactCompanyId: string;
  connectionId: string;
  entityType: SyncedEntityType;
  success: boolean;
  created: number;
  updated: number;
  total: number;
  error?: string;
  duration: number;
}

/**
 * Get all active connections for cron sync
 */
const getActiveConnections = async () => {
  try {
    const connections = await prisma.accountingPlatformIntegration.findMany({
      where: {
        connectionStatus: "ACTIVE",
      },
      select: {
        id: true,
        zactCompanyId: true,
        companyName: true,
      },
    });

    logger.info(`Found ${connections.length} active connections for cron sync`);
    return connections;
  } catch (error) {
    logger.error("Failed to get active connections for cron sync", {
      error: error instanceof Error ? error.message : "Unknown error",
    });
    return [];
  }
};

/**
 * Perform incremental sync for a single connection and entity type with retry logic
 */
const performSyncWithRetry = async (
  connectionId: string,
  zactCompanyId: string,
  entityType: SyncedEntityType,
  attempt: number = 1
): Promise<CronSyncResult> => {
  const startTime = Date.now();

  try {
    // Get valid QBO token
    const { accessToken, realmId } = await getValidQboToken(connectionId);

    // Get the last sync state to determine the start date
    const entitySyncState = await prisma.entitySyncState.findUnique({
      where: {
        connectionId_entityType: {
          connectionId,
          entityType,
        },
      },
    });

    // Use the last successful sync date if available
    const startDate = entitySyncState?.lastSuccessfulSyncAt
      ? new Date(entitySyncState.lastSuccessfulSyncAt)
      : new Date(Date.now() - 24 * 60 * 60 * 1000); // Default to 24 hours ago for scheduled sync

    const formattedDate = startDate.toISOString(); // Send whole date in ISO format

    logger.info(
      `Scheduled sync for ${zactCompanyId} - ${entityType} from ${formattedDate}`,
      {
        connectionId,
        zactCompanyId,
        entityType,
        lastSyncAt: entitySyncState?.lastSuccessfulSyncAt,
        attempt,
      }
    );

    // Call API with date filter
    const apiResponse = await fetchAllDataWithPlanning(
      "qbo",
      entityType.toLowerCase(),
      accessToken,
      realmId,
      connectionId,
      CRON_CONFIG.BATCH_SIZE,
      {
        dateField: "updatedDate",
        startDate: formattedDate,
      }
    );

    const data = apiResponse.data || [];

    if (data.length === 0) {
      logger.info(
        `No records found for scheduled sync ${zactCompanyId} - ${entityType}`,
        {
          connectionId,
          zactCompanyId,
          entityType,
        }
      );

      // Update sync state even if no data
      await updateEntitySyncState(connectionId, entityType, true);

      return {
        zactCompanyId,
        connectionId,
        entityType,
        success: true,
        created: 0,
        updated: 0,
        total: 0,
        duration: Date.now() - startTime,
      };
    }

    // Process data with create/update detection
    const enhancedResult = await processEntityDataWithDetection(
      connectionId,
      entityType,
      data
    );

    // Send scheduled sync Kafka messages with create/update differentiation
    const correlationId = `cron-${Date.now()}-${connectionId}-${entityType}`;
    await sendScheduledSyncData(
      correlationId,
      connectionId,
      entityType,
      enhancedResult.created,
      enhancedResult.updated
    );

    // Update sync state on success
    await updateEntitySyncState(connectionId, entityType, true);

    const duration = Date.now() - startTime;
    const totalRecords =
      enhancedResult.created.length + enhancedResult.updated.length;

    logger.info(
      `Scheduled sync successful for ${zactCompanyId} - ${entityType}`,
      {
        connectionId,
        zactCompanyId,
        entityType,
        created: enhancedResult.created.length,
        updated: enhancedResult.updated.length,
        total: totalRecords,
        duration,
        attempt,
      }
    );

    return {
      zactCompanyId,
      connectionId,
      entityType,
      success: true,
      created: enhancedResult.created.length,
      updated: enhancedResult.updated.length,
      total: totalRecords,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    logger.error(
      `Scheduled sync failed for ${zactCompanyId} - ${entityType} (attempt ${attempt})`,
      {
        connectionId,
        zactCompanyId,
        entityType,
        error: errorMessage,
        duration,
        attempt,
      }
    );

    // Update sync state on failure (only on final attempt)
    if (attempt >= CRON_CONFIG.RETRY_ATTEMPTS) {
      await updateEntitySyncState(connectionId, entityType, false);
    }

    // Retry logic
    if (attempt < CRON_CONFIG.RETRY_ATTEMPTS) {
      logger.info(
        `Retrying scheduled sync for ${zactCompanyId} - ${entityType} in ${CRON_CONFIG.RETRY_DELAY}ms`,
        {
          connectionId,
          zactCompanyId,
          entityType,
          nextAttempt: attempt + 1,
        }
      );

      // Wait before retry
      await new Promise((resolve) =>
        setTimeout(resolve, CRON_CONFIG.RETRY_DELAY)
      );

      return performSyncWithRetry(
        connectionId,
        zactCompanyId,
        entityType,
        attempt + 1
      );
    }

    return {
      zactCompanyId,
      connectionId,
      entityType,
      success: false,
      created: 0,
      updated: 0,
      total: 0,
      error: errorMessage,
      duration,
    };
  }
};

/**
 * Process connections in batches to avoid overwhelming the system
 */
const processBatch = async (
  connections: any[],
  entityType: SyncedEntityType
): Promise<CronSyncResult[]> => {
  const results: CronSyncResult[] = [];

  for (let i = 0; i < connections.length; i += CRON_CONFIG.MAX_CONCURRENT) {
    const batch = connections.slice(i, i + CRON_CONFIG.MAX_CONCURRENT);

    logger.info(
      `Processing batch ${
        Math.floor(i / CRON_CONFIG.MAX_CONCURRENT) + 1
      } for ${entityType}`,
      {
        batchSize: batch.length,
        entityType,
        totalConnections: connections.length,
      }
    );

    const batchPromises = batch.map((connection) =>
      performSyncWithRetry(connection.id, connection.zactCompanyId, entityType)
    );

    const batchResults = await Promise.allSettled(batchPromises);

    batchResults.forEach((result, index) => {
      if (result.status === "fulfilled") {
        results.push(result.value);
      } else {
        const connection = batch[index];
        logger.error(
          `Batch processing failed for ${connection.zactCompanyId} - ${entityType}`,
          {
            connectionId: connection.id,
            zactCompanyId: connection.zactCompanyId,
            entityType,
            error: result.reason,
          }
        );

        results.push({
          zactCompanyId: connection.zactCompanyId,
          connectionId: connection.id,
          entityType,
          success: false,
          created: 0,
          updated: 0,
          total: 0,
          error: result.reason,
          duration: 0,
        });
      }
    });

    // Small delay between batches to prevent overwhelming the system
    if (i + CRON_CONFIG.MAX_CONCURRENT < connections.length) {
      await new Promise((resolve) => setTimeout(resolve, 1000)); // 1 second delay
    }
  }

  return results;
};

/**
 * Send sync completion message for scheduled sync
 */
const sendScheduledSyncCompletionMessage = async (
  connectionId: string,
  zactCompanyId: string,
  entityResults: CronSyncResult[]
): Promise<void> => {
  try {
    // Calculate entity counts for the completion message
    const entityCounts: Record<string, number> = {};

    entityResults.forEach((result) => {
      if (result.success) {
        const entityKey = result.entityType.toLowerCase();
        entityCounts[entityKey] = result.total;
      }
    });

    // Create correlation ID for the completion message
    const correlationId = `scheduled-sync-completion-${Date.now()}-${connectionId}`;

    // Create sync completion message
    const completionMessage = createSyncInfoMessage(
      correlationId,
      connectionId,
      entityCounts
    );

    // Add scheduled sync specific information
    completionMessage.payload.syncType = "SCHEDULED_SYNC";
    completionMessage.payload.message = `Scheduled sync completed for ${
      Object.keys(entityCounts).length
    } entity types. Total records: ${Object.values(entityCounts).reduce(
      (sum, count) => sum + count,
      0
    )}`;

    // Send to sync-completion-response topic
    await baseProducer.sendMessage(
      "sync-completion-response",
      completionMessage,
      connectionId
    );

    logger.info(`Sent scheduled sync completion message`, {
      connectionId,
      zactCompanyId,
      correlationId,
      entityCounts,
      totalRecords: Object.values(entityCounts).reduce(
        (sum, count) => sum + count,
        0
      ),
    });
  } catch (error) {
    logger.error(`Failed to send scheduled sync completion message`, {
      connectionId,
      zactCompanyId,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Main cron job function
 */
const runIncrementalSyncCron = async () => {
  const jobStartTime = Date.now();

  logger.info("Starting incremental sync cron job", {
    schedule: CRON_CONFIG.SCHEDULE,
    entityTypes: CRON_CONFIG.ENTITY_TYPES,
    batchSize: CRON_CONFIG.BATCH_SIZE,
    maxConcurrent: CRON_CONFIG.MAX_CONCURRENT,
  });

  try {
    // Get all active connections
    const connections = await getActiveConnections();

    if (connections.length === 0) {
      logger.info("No active connections found for cron sync");
      return;
    }

    const allResults: CronSyncResult[] = [];

    // Process each entity type
    for (const entityType of CRON_CONFIG.ENTITY_TYPES) {
      logger.info(`Starting cron sync for entity type: ${entityType}`, {
        entityType,
        connectionsCount: connections.length,
      });

      const entityResults = await processBatch(connections, entityType);
      allResults.push(...entityResults);

      logger.info(`Completed cron sync for entity type: ${entityType}`, {
        entityType,
        successful: entityResults.filter((r) => r.success).length,
        failed: entityResults.filter((r) => !r.success).length,
        totalRecords: entityResults.reduce((sum, r) => sum + r.total, 0),
      });
    }

    // Send sync completion messages for each connection
    const connectionGroups = new Map<string, CronSyncResult[]>();

    // Group results by connection
    allResults.forEach((result) => {
      if (!connectionGroups.has(result.connectionId)) {
        connectionGroups.set(result.connectionId, []);
      }
      connectionGroups.get(result.connectionId)!.push(result);
    });

    // Send completion message for each connection
    for (const [connectionId, connectionResults] of connectionGroups) {
      const connection = connections.find((c) => c.id === connectionId);
      if (connection) {
        await sendScheduledSyncCompletionMessage(
          connectionId,
          connection.zactCompanyId,
          connectionResults
        );
      }
    }

    // Generate summary
    const summary = {
      totalConnections: connections.length,
      totalEntityTypes: CRON_CONFIG.ENTITY_TYPES.length,
      totalOperations: allResults.length,
      successful: allResults.filter((r) => r.success).length,
      failed: allResults.filter((r) => !r.success).length,
      totalRecordsProcessed: allResults.reduce((sum, r) => sum + r.total, 0),
      totalCreated: allResults.reduce((sum, r) => sum + r.created, 0),
      totalUpdated: allResults.reduce((sum, r) => sum + r.updated, 0),
      duration: Date.now() - jobStartTime,
    };

    logger.info("Incremental sync cron job completed", summary);

    // Log any failures for monitoring
    const failures = allResults.filter((r) => !r.success);
    if (failures.length > 0) {
      logger.warn(`Cron sync had ${failures.length} failures`, {
        failures: failures.map((f) => ({
          zactCompanyId: f.zactCompanyId,
          entityType: f.entityType,
          error: f.error,
        })),
      });
    }
  } catch (error) {
    const duration = Date.now() - jobStartTime;
    logger.error("Incremental sync cron job failed", {
      error: error instanceof Error ? error.message : "Unknown error",
      duration,
    });
  }
};

/**
 * Initialize and start the cron job
 */
export const initializeIncrementalSyncCron = () => {
  if (!CRON_CONFIG.ENABLED) {
    logger.info("Incremental sync cron job is disabled");
    return null;
  }

  // Validate cron schedule
  if (!cron.validate(CRON_CONFIG.SCHEDULE)) {
    logger.error("Invalid cron schedule provided", {
      schedule: CRON_CONFIG.SCHEDULE,
    });
    return null;
  }

  logger.info("Initializing incremental sync cron job", {
    enabled: CRON_CONFIG.ENABLED,
    schedule: CRON_CONFIG.SCHEDULE,
    entityTypes: CRON_CONFIG.ENTITY_TYPES,
    batchSize: CRON_CONFIG.BATCH_SIZE,
    maxConcurrent: CRON_CONFIG.MAX_CONCURRENT,
  });

  const task = cron.schedule(CRON_CONFIG.SCHEDULE, runIncrementalSyncCron, {
    timezone: CRON_CONFIG.TIMEZONE,
  });

  logger.info("Incremental sync cron job started successfully", {
    schedule: CRON_CONFIG.SCHEDULE,
    timezone: CRON_CONFIG.TIMEZONE,
  });

  return task;
};

/**
 * Stop the cron job (useful for graceful shutdown)
 */
export const stopIncrementalSyncCron = (task: cron.ScheduledTask | null) => {
  if (task) {
    task.stop();
    logger.info("Incremental sync cron job stopped");
  }
};

export default {
  initializeIncrementalSyncCron,
  stopIncrementalSyncCron,
  runIncrementalSyncCron, // Export for manual testing
};
