import {
  ConnectionStatus,
  LogType,
  Prisma,
  RequestMethod,
  SyncStatus,
  SyncedEntityType,
  SyncOperationType,
} from "@prisma/client";
import { backOff } from "exponential-backoff";
import prisma from "../config/db";
import {
  processAndSaveAccounts,
  processAndSaveClasses,
  processAndSaveVendors,
  processAndSaveAccountsWithDetection,
  processAndSaveClassesWithDetection,
  processAndSaveVendorsWithDetection,
} from "../services/platform/qboService";
import ApiException from "./api-exception";
import { logger } from "./logger";
import { ErrorCode, HttpStatus } from "./response";
import { EntityType } from "../interfaces/kafkaMessageInterface";
import baseProducer from "../kafka/producers/base-producer";
import { v4 as uuidv4 } from "uuid";

// Import response handlers
import {
  createSyncSuccessResponse,
  createSyncErrorResponse,
  createSyncCompletionResponse,
} from "../handlers/responseHandlers";

import { fetchAllDataWithPlanning } from "../services/api-gateway/index";
import {
  sendInstantSyncData,
  sendScheduledSyncData,
} from "../services/kafka-message/index";

// Import configuration and constants
import {
  SYNC_CONFIG,
  ENTITY_TYPE_MAPPING as ENTITY_MAPPING,
} from "../constants/syncConfig";
import { KAFKA_TOPICS } from "../constants/kafkaTopics";

// Legacy configuration for backward compatibility
const CONFIG = {
  MAX_RETRIES: SYNC_CONFIG.MAX_RETRIES,
  RETRY_DELAY: SYNC_CONFIG.RETRY_DELAY,
  SYNC_ERROR_TOPIC: KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
};

// Entity type mapping for Kafka messages
const ENTITY_TYPE_MAPPING: Record<SyncedEntityType, EntityType> = {
  [SyncedEntityType.ACCOUNT]: EntityType.ACCOUNT,
  [SyncedEntityType.VENDOR]: EntityType.VENDOR,
  [SyncedEntityType.CLASS]: EntityType.CLASS,
  [SyncedEntityType.BILL]: EntityType.BILL,
  [SyncedEntityType.PAYMENT]: EntityType.PAYMENT,
  [SyncedEntityType.JOURNAL_ENTRY]: EntityType.JOURNAL_ENTRY,
};

// Response creation functions are now imported from handlers/responseHandlers.ts

/**
 * Send sync success message to Kafka topic
 */
const sendSyncSuccessMessage = async (
  connectionId: string,
  entityType: SyncedEntityType,
  entityName: string,
  syncResult: any
): Promise<void> => {
  try {
    // Get zactCompanyId from connection
    const connection = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: connectionId },
      select: { zactCompanyId: true },
    });

    if (!connection) {
      logger.error(`Connection not found for sync success message`, {
        connectionId,
        entityType,
      });
      return;
    }

    const kafkaEntityType = ENTITY_TYPE_MAPPING[entityType];
    const successResponse = createSyncSuccessResponse(
      connection.zactCompanyId,
      kafkaEntityType,
      entityName,
      syncResult
    );

    await baseProducer.sendMessage(
      CONFIG.SYNC_ERROR_TOPIC,
      successResponse,
      connectionId
    );

    logger.info(`Sent sync success message for ${entityType}`, {
      correlationId: successResponse.correlationId,
      connectionId,
      zactCompanyId: connection.zactCompanyId,
      entityType,
      recordsProcessed: syncResult.processed,
    });
  } catch (kafkaError) {
    logger.error(`Failed to send sync success message for ${entityType}`, {
      connectionId,
      entityType,
      kafkaError: kafkaError instanceof Error ? kafkaError.message : kafkaError,
    });
  }
};

/**
 * Send sync error message to Kafka topic
 */
const sendSyncErrorMessage = async (
  connectionId: string,
  entityType: SyncedEntityType,
  entityName: string,
  errorCode: string,
  errorMessage: string,
  errorDetails: any
): Promise<void> => {
  try {
    // Get zactCompanyId from connection
    const connection = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: connectionId },
      select: { zactCompanyId: true },
    });

    if (!connection) {
      logger.error(`Connection not found for sync error message`, {
        connectionId,
        entityType,
      });
      return;
    }

    const kafkaEntityType = ENTITY_TYPE_MAPPING[entityType];
    const errorResponse = createSyncErrorResponse(
      connection.zactCompanyId,
      kafkaEntityType,
      entityName,
      errorCode,
      errorMessage,
      errorDetails
    );

    await baseProducer.sendMessage(
      CONFIG.SYNC_ERROR_TOPIC,
      errorResponse,
      connectionId
    );

    logger.info(`Sent sync error message for ${entityType}`, {
      correlationId: errorResponse.correlationId,
      connectionId,
      zactCompanyId: connection.zactCompanyId,
      errorCode,
      entityType,
    });
  } catch (kafkaError) {
    logger.error(`Failed to send sync error message for ${entityType}`, {
      connectionId,
      entityType,
      originalError: errorMessage,
      kafkaError: kafkaError instanceof Error ? kafkaError.message : kafkaError,
    });
  }
};

/**
 * Validate connection exists and is active
 */
export const validateConnection = async (connectionId: string) => {
  try {
    const connection = await prisma.accountingPlatformIntegration.findUnique({
      where: { id: connectionId },
    });

    if (!connection) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        errorDescription: `Connection with ID ${connectionId} not found`,
      });
    }

    if (connection.connectionStatus !== ConnectionStatus.ACTIVE) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: `Connection ${connectionId} is not active. Status: ${connection.connectionStatus}`,
      });
    }

    return connection;
  } catch (error) {
    if (error instanceof ApiException) {
      throw error;
    }
    logger.error(`Failed to validate connection ${connectionId}: ${error}`);
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      errorDescription: "Failed to validate connection",
    });
  }
};

/**
 * Log API request details with proper error handling
 */
export const logApiRequest = async (
  sourceId: string,
  connectionId: string | null,
  endpoint: string,
  method: RequestMethod,
  details: any,
  status: number,
  message?: string
) => {
  try {
    return await prisma.requestLog.create({
      data: {
        sourceId,
        connectionId,
        logType: LogType.API,
        endpointOrTopic: endpoint,
        methodOrAction: method,
        executionTime: new Date(),
        details,
        status,
        message,
      },
    });
  } catch (error) {
    logger.error(`Failed to log API request: ${error}`);
    // Don't throw here to avoid breaking the main flow
    // Logging failures shouldn't stop the sync process
    return null;
  }
};

/**
 * Create a sync operation record with connection validation
 */
export const createSyncOperation = async (
  connectionId: string,
  entityType: SyncedEntityType,
  syncType: SyncOperationType = SyncOperationType.FULL_SYNC
) => {
  try {
    // Validate connection first
    await validateConnection(connectionId);

    return await prisma.syncOperation.create({
      data: {
        connectionId,
        entityType,
        syncType,
        syncStartedAt: new Date(),
        status: SyncStatus.IN_PROGRESS,
        recordsProcessed: 0,
        recordsSucceeded: 0,
        recordsFailed: 0,
      },
    });
  } catch (error) {
    if (error instanceof ApiException) {
      throw error;
    }
    logger.error(`Failed to create sync operation: ${error}`);
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      errorDescription: "Failed to create sync operation",
    });
  }
};

/**
 * Batch update multiple sync operations in a transaction with error handling
 */
export const batchUpdateSyncOperations = async (
  updates: Array<{
    syncId: string;
    data: Prisma.SyncOperationUpdateInput;
  }>
) => {
  return await prisma.$transaction(async (tx) => {
    const operations = updates.map(({ syncId, data }) =>
      tx.syncOperation.update({
        where: { id: syncId },
        data,
      })
    );
    return await Promise.all(operations);
  });
};

/**
 * Update sync operation status with proper error handling
 */
export const updateSyncOperation = async (
  syncId: string,
  data: {
    status: SyncStatus;
    recordsProcessed?: number;
    recordsSucceeded?: number;
    recordsFailed?: number;
    errorMessage?: string;
    errorCode?: number;
    syncCompletedAt?: Date;
    retryCount?: number;
  }
) => {
  try {
    return await prisma.syncOperation.update({
      where: { id: syncId },
      data,
    });
  } catch (error) {
    logger.error(`Failed to update sync operation ${syncId}: ${error}`);
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      errorDescription: "Failed to update sync operation",
    });
  }
};

/**
 * Update entity sync state with proper error handling
 */
export const updateEntitySyncState = async (
  connectionId: string,
  entityType: SyncedEntityType,
  success: boolean
) => {
  try {
    const now = new Date();
    const dataToUpdate = {
      lastAttemptedSyncAt: now,
      ...(success && { lastSuccessfulSyncAt: now }),
    };

    return await prisma.entitySyncState.upsert({
      where: {
        connectionId_entityType: {
          connectionId,
          entityType,
        },
      },
      update: dataToUpdate,
      create: {
        connectionId,
        entityType,
        lastAttemptedSyncAt: now,
        ...(success && { lastSuccessfulSyncAt: now }),
      },
    });
  } catch (error) {
    logger.error(`Failed to update entity sync state: ${error}`);
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      errorDescription: "Failed to update entity sync state",
    });
  }
};

/**
 * Process entity data (currently only accounts)
 */
export async function processEntityData(
  connectionId: string,
  entityType: SyncedEntityType,
  data: any[]
): Promise<{ processed: number; succeeded: number; failed: number }> {
  switch (entityType) {
    case SyncedEntityType.ACCOUNT:
      return await processAndSaveAccounts(connectionId, data);
    case SyncedEntityType.VENDOR:
      return await processAndSaveVendors(connectionId, data);
    case SyncedEntityType.CLASS:
      return await processAndSaveClasses(connectionId, data);

    // Future entities - returning zero counts for now
    case SyncedEntityType.BILL:
    case SyncedEntityType.PAYMENT:
    case SyncedEntityType.JOURNAL_ENTRY:
      logger.warn(`Sync for entity type ${entityType} is not yet implemented.`);
      return { processed: 0, succeeded: 0, failed: 0 };

    default:
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: `Unknown entity type: ${entityType}`,
      });
  }
}

/**
 * Enhanced process entity data for instant sync with create/update detection
 */
export async function processEntityDataWithDetection(
  connectionId: string,
  entityType: SyncedEntityType,
  data: any[]
): Promise<{
  created: any[];
  updated: any[];
  summary: { processed: number; succeeded: number; failed: number };
}> {
  switch (entityType) {
    case SyncedEntityType.ACCOUNT:
      return await processAndSaveAccountsWithDetection(connectionId, data);
    case SyncedEntityType.VENDOR:
      return await processAndSaveVendorsWithDetection(connectionId, data);
    case SyncedEntityType.CLASS:
      return await processAndSaveClassesWithDetection(connectionId, data);

    // Future entities - returning empty arrays for now
    case SyncedEntityType.BILL:
    case SyncedEntityType.PAYMENT:
    case SyncedEntityType.JOURNAL_ENTRY:
      logger.warn(
        `Entity type ${entityType} not yet implemented for instant sync`
      );
      return {
        created: [],
        updated: [],
        summary: { processed: 0, succeeded: 0, failed: 0 },
      };

    default:
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: `Unknown entity type: ${entityType}`,
      });
  }
}

/**
 * Enhanced sync entity with exponential backoff
 */
export async function syncEntity(
  service: string,
  connectionId: string,
  accessToken: string,
  companyId: string,
  entity: { name: string; type: SyncedEntityType },
  syncType: SyncOperationType = SyncOperationType.FULL_SYNC
): Promise<any> {
  const syncOperation = await createSyncOperation(
    connectionId,
    entity.type,
    syncType
  );

  try {
    logger.info(`Starting sync for ${entity.name} with type ${syncType}`, {
      syncId: syncOperation.id,
      connectionId,
      syncType,
    });

    const result = await backOff(
      async () => {
        const startTime = Date.now();

        let apiResponse;

        // Handle different sync types
        if (syncType === SyncOperationType.FULL_SYNC) {
          // For initial sync, use the existing approach with planning
          apiResponse = await fetchAllDataWithPlanning(
            service,
            entity.name,
            accessToken,
            companyId,
            connectionId,
            1000
          );
        } else if (syncType === SyncOperationType.INCREMENTAL_SYNC) {
          // For instant sync, get the most recent data
          // Get the last sync state to determine the start date
          const entitySyncState = await prisma.entitySyncState.findUnique({
            where: {
              connectionId_entityType: {
                connectionId,
                entityType: entity.type,
              },
            },
          });

          // Use the last successful sync date if available
          const startDate = entitySyncState?.lastSuccessfulSyncAt
            ? new Date(entitySyncState.lastSuccessfulSyncAt)
            : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Default to 7 days ago

          const formattedDate = startDate.toISOString(); // Send whole date in ISO format

          logger.info(`Instant sync for ${entity.name} from ${formattedDate}`, {
            syncId: syncOperation.id,
            lastSyncAt: entitySyncState?.lastSuccessfulSyncAt,
          });

          // Call API with date filter
          apiResponse = await fetchAllDataWithPlanning(
            service,
            entity.name,
            accessToken,
            companyId,
            connectionId,
            1000,
            {
              dateField: "updatedDate", // Use appropriate date field
              startDate: formattedDate,
            }
          );

          // If no records found, return empty result
          if (!apiResponse.data || apiResponse.data.length === 0) {
            return {
              processed: 0,
              succeeded: 0,
              failed: 0,
              duration: Date.now() - startTime,
              throughput: 0,
            };
          }
        } else if (syncType === SyncOperationType.SCHEDULED_SYNC) {
          // For scheduled sync, use a shorter time window
          const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours
          const formattedDate = startDate.toISOString(); // Send whole date in ISO format

          logger.info(
            `Scheduled sync for ${entity.name} from ${formattedDate}`,
            {
              syncId: syncOperation.id,
            }
          );

          // Call API with date filter
          apiResponse = await fetchAllDataWithPlanning(
            service,
            entity.name,
            accessToken,
            companyId,
            connectionId,
            1000,
            {
              dateField: "updatedDate", // Use appropriate date field
              startDate: formattedDate,
            }
          );

          // If no records found, return empty result

          if (!apiResponse.data || apiResponse.data.length === 0) {
            return {
              processed: 0,
              succeeded: 0,
              failed: 0,
              duration: Date.now() - startTime,
              throughput: 0,
            };
          }
        } else {
          // Fallback for any other sync type
          apiResponse = await fetchAllDataWithPlanning(
            service,
            entity.name,
            accessToken,
            companyId,
            connectionId,
            1000
          );
        }

        const data = apiResponse.data || [];

        logger.info(
          `Fetched ${data.length} ${entity.name} records for ${syncType}`
        );

        // Use enhanced processing for instant sync to detect create/update
        if (syncType === SyncOperationType.INCREMENTAL_SYNC) {
          const enhancedResult = await processEntityDataWithDetection(
            connectionId,
            entity.type,
            data
          );

          // Send instant sync Kafka messages with create/update differentiation
          if (
            enhancedResult.created.length > 0 ||
            enhancedResult.updated.length > 0
          ) {
            try {
              await sendInstantSyncData(
                uuidv4(), // correlationId
                connectionId,
                entity.type,
                enhancedResult.created,
                enhancedResult.updated
              );

              logger.info(
                `Sent instant sync Kafka messages for ${entity.name}`,
                {
                  created: enhancedResult.created.length,
                  updated: enhancedResult.updated.length,
                  total:
                    enhancedResult.created.length +
                    enhancedResult.updated.length,
                }
              );
            } catch (kafkaError: any) {
              logger.error(
                `Failed to send instant sync Kafka messages for ${entity.name}`,
                {
                  error: kafkaError.message,
                  created: enhancedResult.created.length,
                  updated: enhancedResult.updated.length,
                }
              );
              // Don't fail the sync operation due to Kafka errors
            }
          }

          const duration = Date.now() - startTime;
          const throughput =
            enhancedResult.summary.processed > 0
              ? enhancedResult.summary.processed / (duration / 1000)
              : 0;

          return {
            ...enhancedResult.summary,
            duration,
            throughput,
          };
        } else {
          // Use regular processing for other sync types
          const processResult = await processEntityData(
            connectionId,
            entity.type,
            data
          );

          const duration = Date.now() - startTime;
          const throughput =
            processResult.processed > 0
              ? processResult.processed / (duration / 1000)
              : 0;

          return {
            ...processResult,
            duration,
            throughput,
          };
        }
      },
      {
        numOfAttempts: syncOperation.maxRetries || 3,
        startingDelay: 1000,
        maxDelay: 30000,
        timeMultiple: 2,
        jitter: "full",
        retry: (error: any) => {
          // Only retry on server errors or network issues
          if (error.isRetryable === false) return false;
          if (error.status && error.status >= 400 && error.status < 500)
            return false;
          return true;
        },
      }
    );

    // Success case - return result with sync operation for batch processing
    await updateEntitySyncState(connectionId, entity.type, true);

    logger.info(`Sync completed for ${entity.name}`, {
      syncId: syncOperation.id,
      ...result,
    });

    // Individual success messages removed - only final completion message will be sent

    return {
      entity: entity.name,
      syncId: syncOperation.id,
      syncOperation,
      status: "COMPLETED",
      processed: result.processed,
      succeeded: result.succeeded,
      failed: result.failed,
      duration: result.duration,
      throughput: result.throughput,
      updateData: {
        status: SyncStatus.COMPLETED,
        recordsProcessed: result.processed,
        recordsSucceeded: result.succeeded,
        recordsFailed: result.failed,
        syncCompletedAt: new Date(),
      },
    };
  } catch (error: any) {
    let code: number | undefined;
    let description: string | undefined;

    if (error instanceof ApiException) {
      code = error.status || error.code;
      description = error.errorDescription;
    } else if (error?.errorData?.error) {
      // Handle serialized or wrapped error format
      code = error.errorData.error.code;
      description = error.errorData.error.errorDescription;
    }

    // Winston log with structured details
    logger.error(`Sync for ${entity.name} failed after all retries`, {
      syncId: syncOperation.id,
      error: error.message,
      code,
      description,
      stack: error.stack,
    });

    await updateEntitySyncState(connectionId, entity.type, false);

    // Send sync error message to Kafka topic
    await sendSyncErrorMessage(
      connectionId,
      entity.type,
      entity.name,
      "SYNC_ERROR",
      `Sync failed for ${entity.name} after all retries`,
      {
        syncId: syncOperation.id,
        entityType: entity.type,
        entityName: entity.name,
        errorCode: code || "UNKNOWN_ERROR",
        errorMessage: error.message || description || "Unknown error",
        retryAttempts: syncOperation.maxRetries || 3,
      }
    );

    return {
      entity: entity.name,
      syncId: syncOperation.id,
      syncOperation,
      status: "FAILED",
      error: error.message || "Unknown error",
      updateData: {
        status: SyncStatus.FAILED,
        errorMessage: error.message || description || "Unknown error",
        errorCode: code || "UNKNOWN_ERROR",
        syncCompletedAt: new Date(),
      },
    };
  }
}

/**
 * Get sync status for monitoring
 */
export const getSyncStatus = async (connectionId: string) => {
  // Validate connection exists
  await validateConnection(connectionId);

  const activeSyncs = await prisma.syncOperation.findMany({
    where: {
      connectionId,
      status: SyncStatus.IN_PROGRESS,
    },
    orderBy: {
      syncStartedAt: "desc",
    },
  });

  const recentSyncs = await prisma.syncOperation.findMany({
    where: {
      connectionId,
      syncCompletedAt: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      },
    },
    orderBy: {
      syncCompletedAt: "desc",
    },
    take: 10,
  });

  const syncStates = await prisma.entitySyncState.findMany({
    where: { connectionId },
  });

  return {
    activeSyncs,
    recentSyncs,
    syncStates,
  };
};

/**
 * Perform instant sync for a specific entity via API with batching support
 * Returns data in simplified Kafka message format
 */
export const performInstantSyncForApi = async (
  connectionId: string,
  entityType: SyncedEntityType,
  accessToken: string,
  companyId: string,
  batchSize: number = 1000
): Promise<{
  data: {
    create: { [key: string]: any[] };
    update: { [key: string]: any[] };
  };
  message: string;
  summary: {
    created: number;
    updated: number;
    total: number;
  };
  batches?: {
    totalBatches: number;
    batchSize: number;
  };
}> => {
  try {
    // Validate connection exists
    await validateConnection(connectionId);

    const startTime = Date.now();

    // Get the last sync state to determine the start date
    const entitySyncState = await prisma.entitySyncState.findUnique({
      where: {
        connectionId_entityType: {
          connectionId,
          entityType,
        },
      },
    });

    // Use the last successful sync date if available
    const startDate = entitySyncState?.lastSuccessfulSyncAt
      ? new Date(entitySyncState.lastSuccessfulSyncAt)
      : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // Default to 7 days ago

    const formattedDate = startDate.toISOString(); // Send whole date in ISO format

    logger.info(`API instant sync for ${entityType} from ${formattedDate}`, {
      connectionId,
      lastSyncAt: entitySyncState?.lastSuccessfulSyncAt,
    });

    // Call API with date filter
    const apiResponse = await fetchAllDataWithPlanning(
      "qbo",
      entityType.toLowerCase(),
      accessToken,
      companyId,
      connectionId,
      1000,
      {
        dateField: "updatedDate",
        startDate: formattedDate,
      }
    );

    const data = apiResponse.data || [];

    if (data.length === 0) {
      logger.info(`No records found for API instant sync ${entityType}`, {
        connectionId,
      });

      const entityKey = entityType.toLowerCase();
      return {
        data: {
          create: {
            [entityKey]: [],
          },
          update: {
            [entityKey]: [],
          },
        },
        message: `No new or updated ${entityType} records found`,
        summary: {
          created: 0,
          updated: 0,
          total: 0,
        },
      };
    }

    // Process data with create/update detection
    const enhancedResult = await processEntityDataWithDetection(
      connectionId,
      entityType,
      data
    );

    // Update sync state on success
    await updateEntitySyncState(connectionId, entityType, true);

    const totalRecords =
      enhancedResult.created.length + enhancedResult.updated.length;

    // Implement batching for large datasets
    const entityKey = entityType.toLowerCase();
    let organizedData;
    let batchInfo;

    if (totalRecords > batchSize) {
      // If data exceeds batch size, implement batching
      const createdBatches = [];
      const updatedBatches = [];

      // Batch created records
      for (let i = 0; i < enhancedResult.created.length; i += batchSize) {
        createdBatches.push(enhancedResult.created.slice(i, i + batchSize));
      }

      // Batch updated records
      for (let i = 0; i < enhancedResult.updated.length; i += batchSize) {
        updatedBatches.push(enhancedResult.updated.slice(i, i + batchSize));
      }

      const totalBatches = Math.max(
        createdBatches.length,
        updatedBatches.length
      );

      // For API response, return first batch and indicate batching
      organizedData = {
        create: {
          [entityKey]: createdBatches[0] || [],
        },
        update: {
          [entityKey]: updatedBatches[0] || [],
        },
      };

      batchInfo = {
        totalBatches,
        batchSize,
        currentBatch: 1,
        hasMoreBatches: totalBatches > 1,
      };

      logger.info(`API instant sync with batching for ${entityType}`, {
        connectionId,
        totalRecords,
        totalBatches,
        batchSize,
        firstBatchSize:
          (createdBatches[0]?.length || 0) + (updatedBatches[0]?.length || 0),
      });
    } else {
      // Normal response for smaller datasets
      organizedData = {
        create: {
          [entityKey]: enhancedResult.created,
        },
        update: {
          [entityKey]: enhancedResult.updated,
        },
      };
    }

    logger.info(`API instant sync completed for ${entityType}`, {
      connectionId,
      created: enhancedResult.created.length,
      updated: enhancedResult.updated.length,
      total: totalRecords,
      duration: Date.now() - startTime,
      batched: totalRecords > batchSize,
    });

    const response: any = {
      data: organizedData,
      message: `Instant sync completed for ${entityType}: ${
        enhancedResult.created.length
      } created, ${enhancedResult.updated.length} updated${
        batchInfo ? ` (showing batch 1 of ${batchInfo.totalBatches})` : ""
      }`,
      summary: {
        created: enhancedResult.created.length,
        updated: enhancedResult.updated.length,
        total: totalRecords,
      },
    };

    if (batchInfo) {
      response.batches = batchInfo;
    }

    return response;
  } catch (error) {
    logger.error(`API instant sync failed for ${entityType}`, {
      connectionId,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    // Update sync state on failure
    await updateEntitySyncState(connectionId, entityType, false);

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      errorDescription: `Instant sync failed for ${entityType}: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    });
  }
};
