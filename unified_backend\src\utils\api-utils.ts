import axios, { AxiosInstance } from "axios";
import logger from "./logger";
import { envConfig } from "../config/config";

// Create axios instance with connection pooling
export const axiosInstance: AxiosInstance = axios.create({
  timeout: envConfig.apiGateway.timeout,
  headers: {
    "Content-Type": "application/json",
  },
  httpAgent: new (require("http").Agent)({
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
  }),
  httpsAgent: new (require("https").Agent)({
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
  }),
});

// Add request/response interceptors for logging
axiosInstance.interceptors.request.use(
  (config) => {
    logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    logger.error(`API Request Error: ${error.message}`);
    return Promise.reject(error);
  }
);
