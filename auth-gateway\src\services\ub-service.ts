import { axiosInstance } from "../utils/api-utils";
import { envConfig } from "../config/config";
import {
  UbTokenRequest,
  UbTokenResponse,
  UbDisconnectRequest,
  UbDisconnectResponse,
} from "../interfaces/auth";
import ApiException from "../utils/api-exception";
import { ErrorCodes, HttpStatus, ErrorCode } from "../utils/response";
import logger from "../utils/logger";

/**
 * Service for communicating with the Unified Backend
 */
export class UbService {
  private baseUrl: string;
  private apiKey: string;
  private timeout: number;

  constructor() {
    this.baseUrl = envConfig.unifiedBackend.baseUrl;
    this.apiKey = envConfig.unifiedBackend.apiKey;
    this.timeout = envConfig.unifiedBackend.timeout;
  }

  /**
   * Store authentication tokens in the Unified Backend
   */
  async storeTokens(tokenRequest: UbTokenRequest): Promise<UbTokenResponse> {
    try {
      logger.info(`Storing tokens in UB for orgId: ${tokenRequest.orgId}`);

      const response = await axiosInstance.post(
        `${this.baseUrl}/auth/store-tokens`,
        tokenRequest,
        {
          headers: {
            "Content-Type": "application/json",
            ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
          },
          timeout: this.timeout,
        }
      );

      if (
        !response.data ||
        !response.data.data ||
        !response.data.data.connectionId
      ) {
        throw new ApiException({
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: "Invalid response from Unified Backend",
        });
      }

      logger.info(
        `Successfully stored tokens in UB, connectionId: ${response.data.data.connectionId}`
      );

      return {
        connectionId: response.data.data.connectionId,
        message: response.data.message || "Tokens stored successfully",
      };
    } catch (error: any) {
      logger.error("Error storing tokens in UB:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      // Handle axios errors
      if (error.response) {
        throw new ApiException({
          status: error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: `UB API error: ${
            error.response.data?.message || error.message
          }`,
        });
      }

      throw new ApiException({
        status: HttpStatus.SERVICE_UNAVAILABLE,
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: "Unable to communicate with Unified Backend",
      });
    }
  }

  /**
   * Get authentication tokens from the Unified Backend
   */
  async getTokens(connectionId: string): Promise<{
    accessToken: string;
    refreshToken: string;
    realmId: string;
  }> {
    try {
      logger.info(`Getting tokens from UB for connectionId: ${connectionId}`);

      const response = await axiosInstance.get(
        `${this.baseUrl}/auth/get-tokens/${connectionId}`,
        {
          headers: {
            "Content-Type": "application/json",
            ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
          },
          timeout: this.timeout,
        }
      );

      if (!response.data || !response.data.data) {
        throw new ApiException({
          status: HttpStatus.NOT_FOUND,
          code: ErrorCode.NOT_FOUND,
          message: "Connection not found in Unified Backend",
        });
      }

      const { accessToken, refreshToken, realmId } = response.data.data;

      if (!accessToken || !refreshToken || !realmId) {
        throw new ApiException({
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: "Incomplete token data from Unified Backend",
        });
      }

      logger.info(
        `Successfully retrieved tokens from UB for connectionId: ${connectionId}`
      );

      return {
        accessToken,
        refreshToken,
        realmId,
      };
    } catch (error: any) {
      logger.error("Error getting tokens from UB:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      // Handle axios errors
      if (error.response) {
        if (error.response.status === 404) {
          throw new ApiException({
            status: HttpStatus.NOT_FOUND,
            code: ErrorCode.NOT_FOUND,
            message: "Connection not found",
          });
        }

        throw new ApiException({
          status: error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: `UB API error: ${
            error.response.data?.message || error.message
          }`,
        });
      }

      throw new ApiException({
        status: HttpStatus.SERVICE_UNAVAILABLE,
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: "Unable to communicate with Unified Backend",
      });
    }
  }

  /**
   * Update authentication tokens in the Unified Backend
   */
  async updateTokens(updateRequest: {
    connectionId: string;
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
  }): Promise<{ message: string }> {
    try {
      logger.info(
        `Updating tokens in UB for connectionId: ${updateRequest.connectionId}`
      );

      const response = await axiosInstance.put(
        `${this.baseUrl}/auth/update-tokens`,
        updateRequest,
        {
          headers: {
            "Content-Type": "application/json",
            ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
          },
          timeout: this.timeout,
        }
      );

      logger.info(
        `Successfully updated tokens in UB for connectionId: ${updateRequest.connectionId}`
      );

      return {
        message: response.data.message || "Tokens updated successfully",
      };
    } catch (error: any) {
      logger.error("Error updating tokens in UB:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      // Handle axios errors
      if (error.response) {
        throw new ApiException({
          status: error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: `UB API error: ${
            error.response.data?.message || error.message
          }`,
        });
      }

      throw new ApiException({
        status: HttpStatus.SERVICE_UNAVAILABLE,
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: "Unable to communicate with Unified Backend",
      });
    }
  }

  /**
   * Delete connection from the Unified Backend
   */
  async deleteConnection(connectionId: string): Promise<UbDisconnectResponse> {
    try {
      logger.info(
        `Deleting connection from UB for connectionId: ${connectionId}`
      );

      const response = await axiosInstance.delete(
        `${this.baseUrl}/auth/delete-connection/${connectionId}`,
        {
          headers: {
            "Content-Type": "application/json",
            ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
          },
          timeout: this.timeout,
        }
      );

      logger.info(
        `Successfully deleted connection from UB for connectionId: ${connectionId}`
      );

      return {
        message: response.data?.message || "Connection deleted successfully",
      };
    } catch (error: any) {
      logger.error("Error deleting connection from UB:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      // Handle axios errors
      if (error.response) {
        if (error.response.status === 404) {
          // Connection already deleted or doesn't exist
          logger.warn(
            `Connection ${connectionId} not found in UB, considering as already deleted`
          );
          return {
            message: "Connection already deleted or not found",
          };
        }

        throw new ApiException({
          status: error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: `UB API error: ${
            error.response.data?.message || error.message
          }`,
        });
      }

      throw new ApiException({
        status: HttpStatus.SERVICE_UNAVAILABLE,
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: "Unable to communicate with Unified Backend",
      });
    }
  }

  /**
   * Health check for Unified Backend connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/health`, {
        timeout: 5000, // Short timeout for health check
      });

      return response.status === 200;
    } catch (error) {
      logger.warn("UB health check failed:", error);
      return false;
    }
  }
}
