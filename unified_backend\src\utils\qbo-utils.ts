import axios from "axios";
import { SyncOperationType } from "@prisma/client";
import logger from "./logger";
import { executeFullSyncWithMessaging } from "../services/kafka-message/index";

/**
 * Helper function to start a background sync process for QBO
 * @param connectionId - The connection ID to sync
 * @param accessToken - The access token for QBO API authentication
 * @param realmId - The QBO realm ID
 * @param syncType - The type of sync operation to perform
 * @param context - Optional context information for logging
 */
export function startQboBackgroundSync(
  connectionId: string,
  accessToken: string,
  realmId: string,
  syncType: SyncOperationType,
  context: string = ""
): void {
  const contextPrefix = context ? `${context} ` : "";

  // Start the sync process in the background
  (async () => {
    try {
      logger.info(
        `Starting ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`
      );

      await executeFullSyncWithMessaging(
        "qbo",
        connectionId,
        accessToken,
        realmId,
        syncType
      );

      logger.info(
        `${syncType} sync completed for ${contextPrefix}QBO connection ${connectionId}`
      );
    } catch (syncError) {
      logger.error(
        `Error during ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`,
        {
          error: syncError,
          connectionId,
          syncType,
        }
      );
    }
  })();
}

/**
 * Fetch company information from QBO
 * @param accessToken - QBO access token
 * @param realmId - QBO realm ID
 * @param baseUrl - QBO API base URL
 * @returns Company name
 */
export async function fetchQboCompanyInfo(
  accessToken: string,
  realmId: string,
  baseUrl: string
): Promise<string> {
  const response = await axios.get(
    `${baseUrl}/${realmId}/companyinfo/${realmId}`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    }
  );
  return response.data.CompanyInfo.CompanyName;
}
