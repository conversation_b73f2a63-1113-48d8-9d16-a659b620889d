#!/bin/bash

# Script to create all Kafka topics used by the Zact Unified Backend
# This ensures all topics are visible in Kafka UI even before messages are sent

echo "🚀 Creating Kafka topics for Zact Unified Backend..."

# Kafka broker address
KAFKA_BROKER="localhost:9092"

# Wait for <PERSON><PERSON><PERSON> to be ready
echo "⏳ Waiting for Kafka to be ready..."
timeout 60 bash -c 'until kafka-topics --bootstrap-server '$KAFKA_BROKER' --list > /dev/null 2>&1; do sleep 2; done'

if [ $? -ne 0 ]; then
    echo "❌ Kafka is not ready after 60 seconds. Please check your Kafka setup."
    exit 1
fi

echo "✅ Kafka is ready!"

# Function to create topic if it doesn't exist
create_topic() {
    local topic_name=$1
    local partitions=${2:-3}
    local replication_factor=${3:-1}
    
    echo "📝 Creating topic: $topic_name"
    
    # Check if topic already exists
    if kafka-topics --bootstrap-server $KAFKA_BROKER --list | grep -q "^$topic_name$"; then
        echo "   ℹ️  Topic $topic_name already exists"
    else
        kafka-topics --bootstrap-server $KAFKA_BROKER \
            --create \
            --topic $topic_name \
            --partitions $partitions \
            --replication-factor $replication_factor
        
        if [ $? -eq 0 ]; then
            echo "   ✅ Topic $topic_name created successfully"
        else
            echo "   ❌ Failed to create topic $topic_name"
        fi
    fi
}

# Main application topics (from kafkaTopics.ts)
echo "📋 Creating main application topics..."
create_topic "entity-create-request" 3 1
create_topic "entity-create-response" 3 1
create_topic "entity-batch-stream" 3 1
create_topic "sync-info" 3 1

# Accounting platform producer topics (from accounting-platform-producer.ts)
echo "📋 Creating accounting platform topics..."
create_topic "unified-backend-sync-completed" 3 1
create_topic "unified-backend-sync-failed" 3 1
create_topic "unified-backend-connection-status-changed" 3 1

# List all topics to verify
echo "📋 Current topics in Kafka:"
kafka-topics --bootstrap-server $KAFKA_BROKER --list

echo "🎉 Topic creation completed!"
echo "💡 You can now view all topics in Kafka UI at http://localhost:8090"
