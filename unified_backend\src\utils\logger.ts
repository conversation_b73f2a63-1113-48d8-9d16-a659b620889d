/**
 * Logger utility using <PERSON>
 */
import winston from "winston";
import { envConfig } from "../config/config";

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log level based on environment
const level = () => {
  const env = envConfig.server.nodeEnv;
  const logLevel =
    envConfig.logging.level || (env === "development" ? "debug" : "info");
  return logLevel;
};

// Define colors for each level
const colors = {
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "blue",
};

// Add colors to winston
winston.addColors(colors);

// Define the format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define the format for file and JSON output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
  winston.format.json()
);

// Define transports
const transports = [
  // Console transport - explicitly ensure it's enabled
  new winston.transports.Console({
    format: consoleFormat,
    level: level(), // Ensure console uses the same level
    silent: false, // Explicitly ensure console is not silenced
  }),
  // File transport for errors
  new winston.transports.File({
    filename: "logs/error.log",
    level: "error",
    format: fileFormat,
  }),
  // File transport for all logs
  new winston.transports.File({
    filename: "logs/combined.log",
    format: fileFormat,
  }),
];

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  transports,
  // Ensure logger is not silenced
  silent: false,
  // Add explicit handling for uncaught exceptions
  handleExceptions: true,
  handleRejections: true,
});

// Add debug information about logger configuration
if (envConfig.server.nodeEnv === "development") {
  logger.debug(`Logger initialized with level: ${level()}`);
  logger.debug(`Console transport enabled: ${!transports[0].silent}`);
  logger.debug(`Number of transports: ${transports.length}`);
}

// Create a stream object for Morgan integration
export const stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

export default logger;
