# Zact Unified Backend & Auth Gateway

This repository contains two main microservices:

## 📁 Project Structure

```
├── auth-gateway/          # QBO Auth Gateway Microservice
│   ├── src/              # Auth Gateway source code
│   ├── package.json      # Auth Gateway dependencies
│   └── .env              # Auth Gateway configuration
│
├── unified_backend/       # Unified Backend Microservice
│   ├── src/              # Unified Backend source code
│   ├── prisma/           # Database schema and migrations
│   ├── package.json      # Unified Backend dependencies
│   └── .env              # Unified Backend configuration
│
└── docs/                 # Shared documentation
```

## 🚀 Services

### Auth Gateway (Port 3001)
- **Purpose**: Handles QBO OAuth 2.0 authentication flows
- **Location**: `./auth-gateway/`
- **Features**: 
  - OAuth URL generation
  - Token exchange
  - Token refresh
  - Token revocation
  - SOC 2-compliant security

### Unified Backend (Port 8080)
- **Purpose**: Main business logic and data processing
- **Location**: `./unified_backend/`
- **Features**:
  - Entity CRUD operations
  - Kafka messaging
  - Database management
  - API Gateway integration
  - Sync operations

## 🔧 Development

### Starting Auth Gateway
```bash
cd auth-gateway
npm install
npm run dev
```

### Starting Unified Backend
```bash
cd unified_backend
npm install
npm run dev
```

## 🔗 Communication

- **Auth Gateway** → **Unified Backend**: Token storage and updates
- **Unified Backend** → **Auth Gateway**: Token refresh requests
- **Frontend** → **Auth Gateway**: Authentication flows
- **Frontend** → **Unified Backend**: Business operations

## 📚 Documentation

See the `docs/` folder for detailed documentation on:
- API communication flows
- Kafka messaging
- Environment configuration
- System architecture

## 🔐 Environment Configuration

Each service has its own `.env` file:
- `auth-gateway/.env` - Auth Gateway configuration
- `unified_backend/.env` - Unified Backend configuration

Make sure to configure both services properly before running.
