# Development Dockerfile for Auth Gateway
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for native modules
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port and debug port
EXPOSE 3001 9229

# Start with nodemon for hot reload and debugging
CMD ["npm", "run", "dev"]
