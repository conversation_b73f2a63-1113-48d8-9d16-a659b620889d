# QBO Token Revocation Implementation

## Overview

This document describes the implementation of QBO (QuickBooks Online) token revocation functionality that automatically revokes access tokens at QBO when a user disconnects their integration.

## Features

### ✅ **Implemented Features**

1. **Automatic Token Revocation**: When disconnecting a QBO integration, the system automatically revokes tokens at QBO
2. **Domain-Based Switch Case**: Uses switch case logic to determine the correct revoke URL based on integration domain
3. **Domain Detection**: Intelligent domain detection from multiple sources (stored data, environment, configuration)
4. **Domain Storage**: Stores domain information in authentication data for future reference
5. **Error Handling**: Graceful error handling that doesn't break disconnection flow
6. **Logging**: Comprehensive logging for debugging and monitoring

## API Endpoints

### Disconnect Integration

```http
POST /auth/disconnect
```

**Headers:**

- `zactcompanyid`: The Zact company ID (required)

**Example:**

```bash
curl -X POST \
  http://localhost:8080/auth/disconnect \
  -H "zactcompanyid: your-company-id"
```

**Response:**

```json
{
  "message": "Successfully disconnected from QBO"
}
```

## Platform Detection Logic

The system determines the accounting platform from the integration:

### 1. Stored Domain (Highest Priority)

```javascript
if (integration?.authentication?.domain) {
  return integration.authentication.domain;
}
```

### 2. Accounting Platform Type

```javascript
if (integration?.accountingPlatformType) {
  return integration.accountingPlatformType.toLowerCase();
}
```

### 3. Default Fallback

```javascript
return "qbo"; // Default to QBO
```

## Switch Case Implementation

The token revocation uses a switch case based on accounting platform:

```javascript
switch (domain.toLowerCase()) {
  case "qbo":
    revokeUrl = "https://developer.api.intuit.com/v2/oauth2/tokens/revoke";
    clientId = envConfig.qbo.clientId;
    clientSecret = envConfig.qbo.clientSecret;
    break;
  case "xero":
    // Future implementation for Xero
    throw new Error("Xero token revocation not implemented yet");
  default:
    // Fallback to QBO for unknown platforms
    revokeUrl = envConfig.qbo.revokeUrl;
    clientId = envConfig.qbo.clientId;
    clientSecret = envConfig.qbo.clientSecret;
    logger.warn(`Unknown platform ${domain}, using QBO revoke URL`);
    break;
}
```

## Token Revocation Process

### 1. Get Latest Tokens

```javascript
const tokenData = await getValidQboTokenByOrgId(zactOrgId);
const accessToken = tokenData.accessToken;
// Get refresh token from updated integration
const updatedIntegration =
  await prisma.accountingPlatformIntegration.findUnique({
    where: { zactCompanyId: zactOrgId },
  });
const refreshToken = updatedIntegration?.authentication?.refreshToken;
```

### 2. Determine Platform

```javascript
const domain = determineAccountingPlatform(integration);
```

### 3. Revoke Tokens at Platform

```javascript
if (accessToken && refreshToken) {
  await revokeTokenByPlatform(refreshToken, domain);
}
```

### 4. Update Local Database

```javascript
await prisma.accountingPlatformIntegration.update({
  where: { id: integration.id },
  data: {
    connectionStatus: ConnectionStatus.INACTIVE,
    authentication: {
      accessToken: null,
      refreshToken: null,
      expiresAt: null,
      lastRefreshedAt: null,
    },
  },
});
```

## Authentication Data Structure

### New Format (With Domain)

```json
{
  "accessToken": "access_token_value",
  "refreshToken": "refresh_token_value",
  "expiresAt": "2024-01-15T10:30:00.000Z",
  "lastRefreshedAt": "2024-01-15T10:30:00.000Z",
  "domain": "sandbox"
}
```

### Legacy Format (Without Domain)

```json
{
  "accessToken": "access_token_value",
  "refreshToken": "refresh_token_value",
  "expiresAt": "2024-01-15T10:30:00.000Z",
  "lastRefreshedAt": "2024-01-15T10:30:00.000Z"
}
```

## Error Handling

### Graceful Degradation

- Token revocation failures don't prevent local disconnection
- Comprehensive error logging for debugging
- User experience is not affected by QBO API issues

### Error Scenarios

1. **Network Issues**: Timeout or connection errors
2. **Invalid Tokens**: Tokens already revoked or expired
3. **QBO API Issues**: Service unavailable or rate limiting
4. **Configuration Issues**: Missing or invalid credentials

### Logging Examples

```javascript
// Success
logger.info("Successfully revoked QBO tokens", {
  domain,
  revokeUrl,
  status: response.status,
});

// Warning
logger.warn("QBO token revocation returned non-200 status", {
  domain,
  revokeUrl,
  status: response.status,
  statusText: response.statusText,
});

// Error
logger.error("Failed to revoke QBO tokens", {
  error: error.message,
  domain,
  status: error.response?.status,
  statusText: error.response?.statusText,
  data: error.response?.data,
});
```

## Configuration

### Environment Variables

```bash
# QBO Configuration
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_ENVIRONMENT=sandbox  # or production
QBO_REVOKE_URL=https://developer.api.intuit.com/v2/oauth2/tokens/revoke

# Base URLs for domain detection
QBO_API_BASE_URL=https://quickbooks.api.intuit.com
QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com
```

## Testing

### Unit Tests

- Domain detection logic
- Switch case functionality
- Error handling scenarios
- Authentication data structure

### Integration Tests

- End-to-end disconnection flow
- QBO API interaction
- Database updates
- Logging verification

## Security Considerations

1. **Token Security**: Tokens are immediately cleared from database after revocation
2. **Credential Protection**: Client credentials are properly encoded in Basic Auth
3. **Error Information**: Sensitive information is not exposed in error messages
4. **Timeout Protection**: API calls have timeout limits to prevent hanging

## Monitoring and Debugging

### Key Metrics

- Token revocation success rate
- API response times
- Error frequency by type
- Domain detection accuracy

### Log Analysis

- Search for "revoked QBO tokens" for successful revocations
- Search for "Failed to revoke QBO tokens" for failures
- Monitor domain detection warnings for configuration issues

## Future Enhancements

1. **Retry Logic**: Implement retry mechanism for transient failures
2. **Batch Revocation**: Support for revoking multiple tokens
3. **Webhook Integration**: QBO webhook support for token status updates
4. **Analytics**: Detailed analytics on revocation patterns
