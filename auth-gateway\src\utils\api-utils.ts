import axios, { AxiosInstance } from "axios";
import logger from "./logger";
import { envConfig } from "../config/config";

// Create axios instance with connection pooling
export const axiosInstance: AxiosInstance = axios.create({
  timeout: envConfig.api.timeout,
  headers: {
    "Content-Type": "application/json",
  },
  httpAgent: new (require("http").Agent)({
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
  }),
  httpsAgent: new (require("https").Agent)({
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
  }),
});

// Request interceptor for logging
axiosInstance.interceptors.request.use(
  (config) => {
    logger.debug(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    logger.error("Request error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging
axiosInstance.interceptors.response.use(
  (response) => {
    logger.debug(`Response ${response.status} from ${response.config.url}`);
    return response;
  },
  (error) => {
    if (error.response) {
      logger.error(`Response error ${error.response.status} from ${error.config?.url}:`, error.response.data);
    } else if (error.request) {
      logger.error("No response received:", error.request);
    } else {
      logger.error("Request setup error:", error.message);
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
