import express from "express";
import asyncHandler from "../utils/async-handler";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";
import { AuthProviderFactory } from "../providers/provider-factory";
import { validateAuthRequest, sanitizeInput } from "../middlewares/validation";
import { auditLogger } from "../middlewares/audit-logger";
import { AuthRequest } from "../interfaces/auth";
import { ProviderType } from "../interfaces/provider";
import logger from "../utils/logger";

const router = express.Router();
const providerFactory = AuthProviderFactory.getInstance();

/**
 * Single dynamic API endpoint for all auth operations
 * Routes based on x-provider header and action in body
 */
router.post(
  "/action",
  auditLogger,
  validateAuthRequest,
  sanitizeInput,
  asyncHandler(async (req, _res) => {
    const providerType = req.get("x-provider") as ProviderType;
    const authRequest: AuthRequest = req.body;
    const orgId = req.get("zactcompanyid");

    // Validate provider
    if (!providerFactory.isProviderSupported(providerType)) {
      throw new ApiException({
        ...ErrorCodes.UNPROCESSABLE_ENTITY,
        errorDescription: `Unsupported provider: ${providerType}. Supported providers: ${providerFactory
          .getSupportedProviders()
          .join(", ")}`,
      });
    }

    // Create provider instance
    const provider = providerFactory.createProvider(providerType);

    logger.info(
      `Processing ${authRequest.action} action for provider: ${providerType}`
    );

    // Route to appropriate action handler
    switch (authRequest.action) {
      case "initiate":
        return await handleInitiateAuth(provider, orgId);

      case "exchange":
        return await handleExchangeToken(provider, authRequest);

      case "refresh":
        return await handleRefreshToken(provider, authRequest);

      case "disconnect":
        return await handleDisconnect(provider, authRequest);

      default:
        throw new ApiException({
          ...ErrorCodes.BAD_REQUEST,
          errorDescription: `Invalid action: ${authRequest.action}. Supported actions: initiate, exchange, refresh, disconnect`,
        });
    }
  })
);

/**
 * Handle auth initiation
 */
async function handleInitiateAuth(provider: any, orgId: string | undefined) {
  if (!orgId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "zactcompanyid header is required for initiate action",
    });
  }

  const result = await provider.generateAuthUrl(orgId);

  logger.info(`Generated auth URL for orgId: ${orgId}`);

  return result;
}

/**
 * Handle token exchange
 */
async function handleExchangeToken(provider: any, authRequest: AuthRequest) {
  const { code, realmId, state } = authRequest;

  // Validate required fields for exchange
  if (!code) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "code is required for exchange action",
    });
  }

  if (!realmId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "realmId is required for exchange action",
    });
  }

  if (!state) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "state is required for exchange action",
    });
  }

  const result = await provider.exchangeCodeForToken(code, realmId, state);

  logger.info(`Exchanged tokens for realmId: ${realmId}, state: ${state}`);

  return result;
}

/**
 * Handle token refresh
 */
async function handleRefreshToken(provider: any, authRequest: AuthRequest) {
  const { connectionId } = authRequest;

  // Validate required fields for refresh
  if (!connectionId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "connectionId is required for refresh action",
    });
  }

  const result = await provider.refreshToken(connectionId);

  logger.info(`Refreshed tokens for connectionId: ${connectionId}`);

  return result;
}

/**
 * Handle disconnect/revoke
 */
async function handleDisconnect(provider: any, authRequest: AuthRequest) {
  const { connectionId } = authRequest;

  // Validate required fields for disconnect
  if (!connectionId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "connectionId is required for disconnect action",
    });
  }

  const result = await provider.revokeToken(connectionId);

  logger.info(`Revoked tokens for connectionId: ${connectionId}`);

  return result;
}

/**
 * Health check endpoint for auth service
 */
router.get(
  "/health",
  asyncHandler(async (_req, _res) => {
    const supportedProviders = providerFactory.getSupportedProviders();

    return {
      message: "Auth service is healthy",
      data: {
        supportedProviders,
        timestamp: new Date().toISOString(),
      },
    };
  })
);

/**
 * Get supported providers endpoint
 */
router.get(
  "/providers",
  asyncHandler(async (_req, _res) => {
    const supportedProviders = providerFactory.getSupportedProviders();

    return {
      message: "Supported authentication providers",
      data: {
        providers: supportedProviders,
        count: supportedProviders.length,
      },
    };
  })
);

export default router;
