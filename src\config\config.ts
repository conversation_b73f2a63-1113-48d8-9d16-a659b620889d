import dotenv from "dotenv";
dotenv.config();

/**
 * Validate required environment variables
 */
const validateRequiredEnvVars = () => {
  const required = ["DATABASE_URL", "JWT_SECRET"];

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
  }
};

// Validate environment variables on import
validateRequiredEnvVars();

export const envConfig = {
  // Server Configuration
  server: {
    port: parseInt(process.env.PORT || "8080"),
    nodeEnv: process.env.NODE_ENV || "development",
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL as string,
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET as string,
    expiresIn: process.env.JWT_EXPIRES_IN || "1d",
  },

  // Kafka Configuration
  kafka: {
    clientId: process.env.KAFKA_CLIENT_ID || "zact-unified-backend",
    brokers: (process.env.KAFKA_BROKERS || "localhost:9092").split(","),
    ssl: process.env.KAFKA_SSL === "true",
    sasl: {
      enabled: process.env.KAFKA_SASL === "true",
      mechanism: process.env.KAFKA_SASL_MECHANISM || "plain",
      username: process.env.KAFKA_SASL_USERNAME || "",
      password: process.env.KAFKA_SASL_PASSWORD || "",
    },
    connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT || "3000"),
    requestTimeout: parseInt(process.env.KAFKA_REQUEST_TIMEOUT || "30000"),
    consumerGroupId:
      process.env.KAFKA_CONSUMER_GROUP_ID || "zact-unified-backend-group",
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || "info",
    fileMaxSize: process.env.LOG_FILE_MAX_SIZE || "10m",
    maxFiles: parseInt(process.env.LOG_MAX_FILES || "7"),
  },

  // Auth Gateway Configuration
  authGateway: {
    baseUrl: process.env.AUTH_GATEWAY_BASE_URL || "http://localhost:3001",
    apiKey: process.env.AUTH_GATEWAY_API_KEY,
    timeout: parseInt(process.env.AUTH_GATEWAY_TIMEOUT || "30000"),
  },

  // QBO API Configuration (for API calls only, not auth)
  qbo: {
    environment: process.env.QBO_ENVIRONMENT || "sandbox",
    baseUrl:
      process.env.QBO_ENVIRONMENT === "production"
        ? process.env.QBO_API_BASE_URL
        : process.env.QBO_API_BASE_URL_SANDBOX,
    baseUrlSandbox: process.env.QBO_API_BASE_URL_SANDBOX,
    baseUrlProduction: process.env.QBO_API_BASE_URL,
  },

  // API Gateway Configuration
  apiGateway: {
    url: process.env.API_GATEWAY_URL || "http://localhost:8000",
    timeout: parseInt(process.env.API_TIMEOUT || "30000"),
  },

  // Sync Configuration
  sync: {
    maxRetries: parseInt(process.env.MAX_SYNC_RETRIES || "3"),
    retryDelay: parseInt(process.env.SYNC_RETRY_DELAY || "1000"),
    batchSize: parseInt(process.env.SYNC_BATCH_SIZE || "50"),
    maxBatchRetries: parseInt(process.env.SYNC_MAX_BATCH_RETRIES || "3"),
    defaultCleanupDays: parseInt(process.env.SYNC_DEFAULT_CLEANUP_DAYS || "90"),
    maxConcurrentSyncs: parseInt(process.env.MAX_CONCURRENT_SYNCS || "3"),
  },

  // Kafka Message Configuration
  kafkaMessage: {
    batchSize: parseInt(process.env.KAFKA_MESSAGE_BATCH_SIZE || "500"),
    batchDelayMs: parseInt(process.env.KAFKA_MESSAGE_BATCH_DELAY_MS || "100"),
  },

  // Cron Job Configuration
  cron: {
    incrementalSync: {
      enabled: process.env.INCREMENTAL_SYNC_CRON_ENABLED === "true",
      schedule: process.env.INCREMENTAL_SYNC_CRON_SCHEDULE || "0 */6 * * *",
      batchSize: parseInt(
        process.env.INCREMENTAL_SYNC_CRON_BATCH_SIZE || "1000"
      ),
      entityTypes: (
        process.env.INCREMENTAL_SYNC_CRON_ENTITIES || "ACCOUNT,VENDOR,CLASS"
      )
        .split(",")
        .map((type) => type.trim().toUpperCase()),
      maxConcurrent: parseInt(
        process.env.INCREMENTAL_SYNC_CRON_MAX_CONCURRENT || "3"
      ),
      retryAttempts: parseInt(
        process.env.INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS || "3"
      ),
      retryDelay: parseInt(
        process.env.INCREMENTAL_SYNC_CRON_RETRY_DELAY || "5000"
      ),
      timezone: process.env.CRON_TIMEZONE || "UTC",
    },
  },
};
