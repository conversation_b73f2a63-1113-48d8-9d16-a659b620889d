import prisma from "../config/db";
import logger from "../utils/logger";
import { IUnifiedJournalEntry } from "../interfaces";

/**
 * Repository for Journal Entry database operations
 */
export class JournalEntryRepository {
  /**
   * Save journal entry to database
   */
  static async save(
    unifiedJournalEntry: IUnifiedJournalEntry,
    connectionId: string
  ): Promise<any> {
    try {
      // Prepare data for database insertion
      const journalEntryData = {
        connectionId: connectionId,
        externalJournalEntryId: unifiedJournalEntry.id,
        transactionDate: new Date(unifiedJournalEntry.transactionDate),
        currencyCode: unifiedJournalEntry.currencyCode,
        totalAmount: unifiedJournalEntry.totalAmount,
        memo: unifiedJournalEntry.memo, // MANDATORY field - include memo
        lineItems: unifiedJournalEntry.lineItems,
        domain: unifiedJournalEntry.domain,
        createdAtPlatform: unifiedJournalEntry.createdAtPlatform,
        updatedAtPlatform: unifiedJournalEntry.updatedAtPlatform,
      };

      // Save to database
      const savedEntity = await prisma.journalEntry.create({
        data: journalEntryData,
      });

      logger.info(`JournalEntry saved to database with ID: ${savedEntity.id}`);

      // Return saved entity in unified response format
      return {
        id: savedEntity.id,
        externalJournalEntryId: savedEntity.externalJournalEntryId,
        transactionDate: savedEntity.transactionDate.toISOString(),
        currencyCode: savedEntity.currencyCode,
        totalAmount: savedEntity.totalAmount,
        memo: savedEntity.memo, // Include memo in response
        lineItems: savedEntity.lineItems,
        domain: savedEntity.domain,
        createdAtPlatform: savedEntity.createdAtPlatform.toISOString(),
        updatedAtPlatform: savedEntity.updatedAtPlatform?.toISOString(),
        connectionId: savedEntity.connectionId,
        createdAt: savedEntity.lastSyncedAt.toISOString(),
      };
    } catch (error) {
      logger.error(`Error saving JournalEntry to database:`, error);
      throw error;
    }
  }

  /**
   * Find journal entries by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.journalEntry.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find journal entry by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.journalEntry.findFirst({
      where: {
        externalJournalEntryId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Update journal entry
   */
  static async update(id: string, data: any) {
    return await prisma.journalEntry.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old journal entries
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.journalEntry.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }
}
