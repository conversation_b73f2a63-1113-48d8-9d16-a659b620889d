import { v4 as uuidv4 } from "uuid";

/**
 * Create success response message for entity operations
 */
export const createSuccessResponse = (
  originalMessage: any,
  entityType: string,
  savedEntity: any,
  originalPayload: any
) => {
  return {
    messageId: uuidv4(),
    correlationId: originalMessage.correlationId,
    timestamp: Date.now(),
    source: "UNIFIED_BACKEND",
    destination: "ZACT_APP",
    messageType: "RESPONSE",
    status: "SUCCESS",
    entityOperation: {
      entityType: entityType,
      operation: "CREATE",
    },
    payload: savedEntity,
    originalPayload: originalPayload,
  };
};

/**
 * Create error response message for entity operations
 */
export const createErrorResponse = (
  originalMessage: any,
  entityType: string,
  errorCode: string,
  errorMessage: string,
  errorDetails: any,
  originalPayload: any
) => {
  return {
    messageId: uuidv4(),
    correlationId: originalMessage.correlationId,
    timestamp: Date.now(),
    source: "UNIFIED_BACKEND",
    destination: "ZACT_APP",
    messageType: "RESPONSE",
    status: "ERROR",
    entityOperation: {
      entityType: entityType,
      operation: "CREATE",
    },
    error: {
      code: errorCode,
      message: errorMessage,
      details: errorDetails,
    },
    originalPayload: originalPayload,
  };
};

/**
 * Create sync success response message
 */
export const createSyncSuccessResponse = (
  zactCompanyId: string,
  entityType: any,
  entityName: string,
  syncResult: any
) => {
  return {
    messageId: uuidv4(),
    correlationId: uuidv4(),
    timestamp: Date.now(),
    source: "UNIFIED_BACKEND",
    destination: "ZACT_APP",
    messageType: "RESPONSE",
    status: "SUCCESS",
    entityOperation: {
      entityType: entityType,
      operation: "SYNC",
    },
    payload: {
      syncId: syncResult.syncId,
      entityType: entityName,
      recordsProcessed: syncResult.processed,
      recordsSucceeded: syncResult.succeeded,
      recordsFailed: syncResult.failed,
      duration: syncResult.duration,
      throughput: syncResult.throughput,
    },
    originalPayload: {
      entityType: entityName,
      zactCompanyId: zactCompanyId,
      syncOperation: "SYNC",
    },
  };
};

/**
 * Create sync error response message
 */
export const createSyncErrorResponse = (
  zactCompanyId: string,
  entityType: any,
  entityName: string,
  errorCode: string,
  errorMessage: string,
  errorDetails: any
) => {
  return {
    messageId: uuidv4(),
    correlationId: uuidv4(),
    timestamp: Date.now(),
    source: "UNIFIED_BACKEND",
    destination: "ZACT_APP",
    messageType: "RESPONSE",
    status: "ERROR",
    entityOperation: {
      entityType: entityType,
      operation: "SYNC",
    },
    error: {
      code: errorCode,
      message: errorMessage,
      details: errorDetails,
    },
    originalPayload: {
      entityType: entityName,
      zactCompanyId: zactCompanyId,
      syncOperation: "SYNC",
    },
  };
};

/**
 * Create sync completion response message
 */
export const createSyncCompletionResponse = (
  zactCompanyId: string,
  entityTypes: string[],
  totalRecords: number,
  duration: number
) => {
  return {
    messageId: uuidv4(),
    correlationId: uuidv4(),
    timestamp: Date.now(),
    source: "UNIFIED_BACKEND",
    destination: "ZACT_APP",
    messageType: "RESPONSE",
    status: "SUCCESS",
    entityOperation: {
      entityType: "ALL",
      operation: "SYNC",
    },
    payload: {
      message: "Sync completed successfully",
      entityTypes: entityTypes,
      totalRecords: totalRecords,
      duration: duration,
      zactCompanyId: zactCompanyId,
    },
  };
};
