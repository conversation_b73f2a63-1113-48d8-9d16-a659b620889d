# QBO Auth Gateway Microservice

A secure authentication gateway microservice for QuickBooks Online (QBO) with extensible architecture for future accounting platforms.

## 🎯 Purpose

This microservice acts as a secure middleware between the Frontend and the Unified Backend (UB), providing:

- **Single Dynamic API Endpoint** for all auth operations
- **Provider-based Architecture** (QBO now, Xero/NetSuite/QBD later)
- **SOC 2 Compliant** security and audit logging
- **Extensible Design** for multiple accounting platforms

## 🏗️ Architecture

### Provider Strategy Pattern
- **Base Provider Interface**: Common contract for all providers
- **QBO Provider**: QuickBooks Online implementation
- **Provider Factory**: Dynamic provider instantiation
- **Future Ready**: Xero, NetSuite, QuickBooks Desktop

### Security Features
- Rate limiting and CORS protection
- Input validation and sanitization
- Audit logging for compliance
- Security headers and CSP
- Request/response monitoring

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- TypeScript
- Access to Unified Backend
- QBO Developer Account

### Installation

```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Configure environment variables
# Edit .env with your QBO credentials and UB settings

# Start development server
npm run dev

# Build for production
npm run build
npm start
```

### Environment Variables

```env
# Server
PORT=3001
NODE_ENV=development

# QBO Configuration
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_REDIRECT_URI=http://localhost:4200/auth/callback
QBO_ENVIRONMENT=sandbox

# Unified Backend
UB_BASE_URL=http://localhost:8080
UB_API_KEY=your_ub_api_key

# Security
CORS_ORIGIN=http://localhost:4200
RATE_LIMIT_MAX_REQUESTS=100
```

## 📡 API Endpoints

### Single Dynamic Endpoint

**POST** `/auth/action`

Headers:
- `x-provider`: Provider type (`qbo`, `xero`, `netsuite`, `qbd`)
- `zactcompanyid`: Organization ID (for initiate action)

#### Initiate Auth
```json
{
  "action": "initiate"
}
```

Response:
```json
{
  "responseStatus": 200,
  "message": "QBO authorization URL generated successfully",
  "data": "https://appcenter.intuit.com/connect/oauth2?..."
}
```

#### Exchange Token
```json
{
  "action": "exchange",
  "code": "auth_code_from_provider",
  "realmId": "provider_company_id",
  "state": "org_id"
}
```

Response:
```json
{
  "responseStatus": 200,
  "message": "Successfully connected to QBO and tokens stored",
  "data": {
    "connectionId": "uuid",
    "realmId": "*********",
    "erpSystem": "qbo"
  }
}
```

#### Disconnect
```json
{
  "action": "disconnect",
  "connectionId": "connection_uuid"
}
```

Response:
```json
{
  "responseStatus": 200,
  "message": "Successfully disconnected from QBO",
  "data": {
    "connectionId": "uuid",
    "erpSystem": "qbo"
  }
}
```

### Utility Endpoints

**GET** `/auth/health` - Service health check
**GET** `/auth/providers` - List supported providers
**GET** `/health` - Application health check

## 🔄 Auth Flows

### 1. Initiate Auth Flow
```
Frontend → Auth Gateway → QBO Provider → Response (Auth URL)
```

### 2. Token Exchange Flow
```
Frontend → Auth Gateway → QBO Provider → QBO API → UB → Response
```

### 3. Disconnect Flow
```
Frontend → Auth Gateway → UB (get token) → QBO API (revoke) → UB (delete) → Response
```

## 🛡️ Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **CORS Protection**: Configurable allowed origins
- **Input Validation**: Request sanitization and validation
- **Audit Logging**: SOC 2 compliant activity logging
- **Security Headers**: Helmet.js protection
- **Error Handling**: Secure error responses

## 📁 Project Structure

```
auth-gateway/
├── src/
│   ├── config/           # Configuration management
│   ├── interfaces/       # TypeScript interfaces
│   ├── middlewares/      # Express middlewares
│   ├── providers/        # Auth provider implementations
│   ├── routes/           # API route handlers
│   ├── services/         # Business logic services
│   ├── utils/            # Utility functions
│   └── index.ts          # Application entry point
├── logs/                 # Application logs
├── .env.example          # Environment template
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

## 🔧 Development

### Adding New Providers

1. Create provider class extending `BaseAuthProvider`
2. Implement required methods: `generateAuthUrl`, `exchangeCodeForToken`, `revokeToken`
3. Register in `AuthProviderFactory`
4. Add provider-specific configuration

Example:
```typescript
export class XeroProvider extends BaseAuthProvider {
  constructor() {
    super("XERO");
  }

  async generateAuthUrl(orgId: string): Promise<AuthUrlResponse> {
    // Xero-specific implementation
  }

  // ... other methods
}
```

### Logging

- **Info**: General application flow
- **Warn**: Potential issues or security events
- **Error**: Application errors and failures
- **Debug**: Detailed debugging information (dev only)

### Monitoring

- Health check endpoints for service monitoring
- Audit logs for compliance tracking
- Performance metrics via request logging
- Error tracking and alerting

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Setup
- Configure production environment variables
- Set up log rotation and monitoring
- Configure reverse proxy (nginx/Apache)
- Set up SSL/TLS certificates

## 🤝 Integration

### Frontend Integration
```javascript
// Initiate auth
const response = await fetch('/auth/action', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-provider': 'qbo',
    'zactcompanyid': 'org-123'
  },
  body: JSON.stringify({ action: 'initiate' })
});
```

### Unified Backend Integration
The service communicates with UB via REST APIs:
- `POST /auth/store-tokens` - Store authentication tokens
- `GET /auth/get-tokens/:connectionId` - Retrieve tokens
- `DELETE /auth/delete-connection/:connectionId` - Delete connection

## 📝 License

ISC License - Internal use only.
