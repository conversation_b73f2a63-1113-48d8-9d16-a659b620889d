// Import third-party libraries
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import compression from "compression";
import { v4 as uuidv4 } from "uuid";

// Import internal modules
import { envConfig } from "./config/config";
import logger from "./utils/logger";
import {
  apiLogger,
  rateLimiter,
  corsOptions,
  auditLogger,
  requestIdMiddleware,
} from "./middlewares";
import { errorHandler, notFoundHandler } from "./utils/error-handler";
import authRoutes from "./routes/auth";

// Load environment variables
dotenv.config();

// Create Express application
const app = express();

// Trust proxy for accurate IP addresses
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  })
);

// CORS middleware
app.use(cors(corsOptions));

// Compression middleware
app.use(compression());

// Rate limiting
app.use(rateLimiter);

// Request tracking middleware
app.use(requestIdMiddleware);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// API logging middleware
app.use(apiLogger);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "qbo-auth-gateway",
    version: "1.0.0",
  });
});

// API routes
app.use("/auth", authRoutes);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start server
const PORT = envConfig.server.port;
const server = app.listen(PORT, () => {
  logger.info(`🚀 QBO Auth Gateway started on port ${PORT}`);
  logger.info(`📝 Environment: ${envConfig.server.nodeEnv}`);
  logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down gracefully");
  server.close(() => {
    logger.info("Process terminated");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  logger.info("SIGINT received, shutting down gracefully");
  server.close(() => {
    logger.info("Process terminated");
    process.exit(0);
  });
});

export default app;
