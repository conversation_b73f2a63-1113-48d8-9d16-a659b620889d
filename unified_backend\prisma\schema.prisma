generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model AccountingPlatformIntegration {
  id                     String            @id @default(uuid()) @map("connection_id")
  zactCompanyId          String            @unique @map("zact_company_id")
  accountingPlatformType String            @map("accounting_Platform_type")
  externalCompanyId      String            @map("external_company_id")
  companyName            String            @map("company_name")
  connectionStatus       ConnectionStatus  @map("connection_status")
  lastConnectedAt        DateTime?         @map("last_connected_at")
  authentication         Json?             @map("authentication")
  scheduledJobs          Json?             @map("scheduled_jobs")
  createdAt              DateTime          @default(now()) @map("created_at")
  updatedAt              DateTime          @updatedAt @map("updated_at")
  accounts               Account[]
  bills                  Bill[]
  classes                Class[]
  entitySyncStates       EntitySyncState[]
  journalEntries         JournalEntry[]
  payments               Payment[]
  requestLogs            RequestLog[]
  syncOperations         SyncOperation[]
  vendors                Vendor[]

  @@unique([accountingPlatformType, externalCompanyId])
  @@index([connectionStatus])
  @@map("accounting_platform_integrations")
}

model EntitySyncState {
  id                   String                        @id @default(uuid())
  connectionId         String                        @map("connection_id")
  entityType           SyncedEntityType              @map("entity_type")
  lastSuccessfulSyncAt DateTime?                     @map("last_successful_sync_at")
  lastAttemptedSyncAt  DateTime?                     @map("last_attempted_sync_at")
  createdAt            DateTime                      @default(now()) @map("created_at")
  updatedAt            DateTime                      @updatedAt @map("updated_at")
  integration          AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, entityType])
  @@index([connectionId])
  @@index([entityType])
  @@map("entity_sync_states")
}

model RequestLog {
  id              String                         @id @default(uuid()) @map("log_id")
  sourceId        String                         @map("source_id")
  connectionId    String?                        @map("connection_id")
  logType         LogType                        @map("log_type")
  endpointOrTopic String                         @map("endpoint_or_topic")
  methodOrAction  String                         @map("method_or_action")
  executionTime   DateTime                       @map("execution_time")
  details         Json?                          @map("details")
  status          Int                            @map("status")
  message         String?                        @map("message")
  createdAt       DateTime                       @default(now()) @map("created_at")
  integration     AccountingPlatformIntegration? @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@index([sourceId])
  @@index([connectionId])
  @@index([logType])
  @@index([createdAt])
  @@map("request_logs")
}

model SyncOperation {
  id               String                        @id @default(uuid()) @map("sync_id")
  connectionId     String                        @map("connection_id")
  entityType       SyncedEntityType              @map("entity_type")
  syncType         SyncOperationType             @map("sync_type")
  syncStartedAt    DateTime                      @map("sync_started_at")
  syncCompletedAt  DateTime?                     @map("sync_completed_at")
  status           SyncStatus                    @map("status")
  recordsProcessed Int                           @default(0) @map("records_processed")
  recordsSucceeded Int                           @default(0) @map("records_succeeded")
  recordsFailed    Int                           @default(0) @map("records_failed")
  errorMessage     String?                       @map("error_message")
  errorCode        Int?                          @map("error_code")
  retryCount       Int                           @default(0) @map("retry_count")
  maxRetries       Int                           @default(3) @map("max_retries")
  createdAt        DateTime                      @default(now()) @map("created_at")
  updatedAt        DateTime                      @updatedAt @map("updated_at")
  integration      AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@index([connectionId])
  @@index([entityType])
  @@index([status])
  @@index([syncStartedAt])
  @@index([connectionId, entityType, syncStartedAt])
  @@map("sync_operations")
}

model Account {
  id                            String                        @id @default(uuid()) @map("account_id")
  connectionId                  String                        @map("connection_id")
  externalAccountId             String                        @map("external_account_id")
  name                          String                        @map("name")
  fullyQualifiedName            String                        @map("fully_qualified_name")
  accountType                   String                        @map("account_type")
  accountSubType                String?                       @map("account_sub_type")
  category                      String?                       @map("category")
  parentId                      String?                       @map("parent_id")
  active                        Boolean                       @map("active")
  currentBalance                Float                         @map("current_balance")
  subAccount                    Boolean                       @map("sub_account")
  currentBalanceWithSubAccounts Float?                        @map("current_balance_with_sub_accounts")
  currencyCode                  String?                       @map("currency_code")
  accountNumber                 String?                       @map("account_number")
  description                   String?                       @map("description")
  bankAccountNumber             String?                       @map("bank_account_number")
  routingNumber                 String?                       @map("routing_number")
  taxType                       String?                       @map("tax_type")
  externalId                    String?                       @map("external_id")
  platformUrl                   String?                       @map("platform_url")
  domain                        String                        @map("domain")
  createdAtPlatform             DateTime                      @map("created_at_platform")
  updatedAtPlatform             DateTime                      @map("updated_at_platform")
  lastSyncedAt                  DateTime                      @updatedAt @map("last_synced_at")
  integration                   AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalAccountId])
  @@index([connectionId])
  @@index([active])
  @@index([name])
  @@map("accounts")
}

model Vendor {
  id                          String                        @id @default(uuid()) @map("vendor_id")
  connectionId                String                        @map("connection_id")
  externalVendorId            String?                       @map("external_vendor_id")
  vendorName                  String                        @map("vendor_name")
  contactName                 String?                       @map("contact_name")
  email                       String?                       @map("email")
  phone                       String?                       @map("phone")
  website                     String?                       @map("website")
  bankAccountNumber           String?                       @map("bank_account_number")
  addresses                   Json?                         @map("addresses")
  isActive                    Boolean                       @map("is_active")
  domain                      String                        @map("domain")
  balance                     Float?                        @map("balance")
  taxNumber                   String?                       @map("tax_number")
  currencyCode                String?                       @map("currency_code")
  paymentTermsId              String?                       @map("payment_terms_id")
  defaultExpenseAccountId     String?                       @map("default_expense_account_id")
  defaultBillPaymentAccountId String?                       @map("default_bill_payment_account_id")
  vendorNumber                String?                       @map("vendor_number")
  externalId                  String?                       @map("external_id")
  platformUrl                 String?                       @map("platform_url")
  createdAtPlatform           DateTime                      @map("created_at_platform")
  updatedAtPlatform           DateTime?                     @map("updated_at_platform")
  lastSyncedAt                DateTime                      @updatedAt @map("last_synced_at")
  integration                 AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalVendorId])
  @@index([connectionId])
  @@index([isActive])
  @@index([vendorName])
  @@map("vendors")
}

model Bill {
  id                String                        @id @default(uuid()) @map("bill_id")
  connectionId      String                        @map("connection_id")
  vendorId          String                        @map("vendor_id")
  externalBillId    String?                       @map("external_bill_id")
  billNumber        String                        @map("bill_number")
  billDate          DateTime                      @map("bill_date")
  dueDate           DateTime                      @map("due_date")
  totalAmount       Float                         @map("total_amount")
  balance           Float                         @map("balance")
  currency          String                        @map("currency")
  status            BillStatus                    @map("status")
  privateNote       String?                       @map("private_note")
  exchangeRate      Float?                        @map("exchange_rate")
  subTotal          Float?                        @map("sub_total")
  taxAmount         Float?                        @map("tax_amount")
  discountAmount    Float?                        @map("discount_amount")
  paymentTermsId    String?                       @map("payment_terms_id")
  referenceNumber   String?                       @map("reference_number")
  externalId        String?                       @map("external_id")
  platformUrl       String?                       @map("platform_url")
  lineItems         Json                          @map("line_items")
  accountId         String?                       @map("account_id")
  classId           String?                       @map("class_id")
  domain            String                        @map("domain")
  createdAtPlatform DateTime                      @map("created_at_platform")
  updatedAtPlatform DateTime?                     @map("updated_at_platform")
  lastSyncedAt      DateTime                      @updatedAt @map("last_synced_at")
  integration       AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalBillId])
  @@index([connectionId])
  @@index([vendorId])
  @@index([status])
  @@index([billDate])
  @@map("bills")
}

model Class {
  id                 String                        @id @default(uuid()) @map("class_id")
  connectionId       String                        @map("connection_id")
  externalClassId    String                        @map("external_class_id")
  name               String                        @map("name")
  hasChildren        Boolean                       @default(false) @map("has_children")
  parentId           String?                       @map("parent_id")
  isActive           Boolean                       @map("is_active")
  fullyQualifiedName String?                       @map("fully_qualified_name")
  classNumber        String?                       @map("class_number")
  description        String?                       @map("description")
  externalId         String?                       @map("external_id")
  platformUrl        String?                       @map("platform_url")
  domain             String                        @map("domain")
  createdAtPlatform  DateTime                      @map("created_at_platform")
  updatedAtPlatform  DateTime?                     @map("updated_at_platform")
  lastSyncedAt       DateTime                      @updatedAt @map("last_synced_at")
  integration        AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalClassId])
  @@index([connectionId])
  @@index([isActive])
  @@index([name])
  @@map("classes")
}

model JournalEntry {
  id                     String                        @id @default(uuid()) @map("journal_entry_id")
  connectionId           String                        @map("connection_id")
  externalJournalEntryId String?                       @map("external_journal_entry_id")
  transactionDate        DateTime                      @map("transaction_date")
  currencyCode           String?                       @map("currency_code")
  totalAmount            Float?                        @map("total_amount")
  memo                   String?                       @map("memo")
  exchangeRate           Float?                        @map("exchange_rate")
  journalNumber          String?                       @map("journal_number")
  postingStatus          String?                       @map("posting_status")
  accountingPeriodId     String?                       @map("accounting_period_id")
  inclusiveOfTax         Boolean?                      @map("inclusive_of_tax")
  companyId              String?                       @map("company_id")
  externalId             String?                       @map("external_id")
  platformUrl            String?                       @map("platform_url")
  lineItems              Json                          @map("line_items")
  domain                 String                        @map("domain")
  createdAtPlatform      DateTime                      @map("created_at_platform")
  updatedAtPlatform      DateTime?                     @map("updated_at_platform")
  lastSyncedAt           DateTime                      @updatedAt @map("last_synced_at")
  integration            AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalJournalEntryId])
  @@index([connectionId])
  @@index([transactionDate])
  @@map("journal_entries")
}

model Payment {
  id                  String                        @id @default(uuid()) @map("payment_id")
  connectionId        String                        @map("connection_id")
  vendorId            String                        @map("vendor_id")
  externalPaymentId   String?                       @map("external_payment_id")
  paymentType         PaymentType                   @map("payment_type")
  totalAmount         Float                         @map("total_amount")
  paymentDate         DateTime                      @map("payment_date")
  currency            String                        @map("currency")
  bankAccountId       String?                       @map("bank_account_id")
  creditCardAccountId String?                       @map("credit_card_account_id")
  checkNumber         String?                       @map("check_number")
  privateNote         String?                       @map("private_note")
  referenceNumber     String?                       @map("reference_number")
  exchangeRate        Float?                        @map("exchange_rate")
  paymentMethodId     String?                       @map("payment_method_id")
  memo                String?                       @map("memo")
  externalId          String?                       @map("external_id")
  platformUrl         String?                       @map("platform_url")
  billPayments        Json                          @map("bill_payments")
  domain              String                        @map("domain")
  createdAtPlatform   DateTime                      @map("created_at_platform")
  updatedAtPlatform   DateTime?                     @map("updated_at_platform")
  lastSyncedAt        DateTime                      @updatedAt @map("last_synced_at")
  integration         AccountingPlatformIntegration @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@unique([connectionId, externalPaymentId])
  @@index([connectionId])
  @@index([vendorId])
  @@index([paymentType])
  @@index([paymentDate])
  @@map("payments")
}

enum ConnectionStatus {
  ACTIVE
  INACTIVE
  PENDING
  ERROR
}

enum BillStatus {
  DRAFT
  OPEN
  PAID
  VOID
}

enum SyncStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum PaymentType {
  CHECK
  CREDIT_CARD
  CASH
}

enum SyncedEntityType {
  ACCOUNT
  BILL
  PAYMENT
  CLASS
  VENDOR
  JOURNAL_ENTRY
}

enum LogType {
  API
  KAFKA
}

enum SyncOperationType {
  FULL_SYNC
  INCREMENTAL_SYNC
  SCHEDULED_SYNC
}

enum RequestMethod {
  GET
  POST
}


