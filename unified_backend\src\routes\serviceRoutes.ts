import express from "express";
import asyncHandler from "../utils/async-handler";
import { services } from "./../services/platform";
import ApiException from "./../utils/api-exception";
import { ErrorCode, HttpStatus } from "./../utils/response";
import { getValidQboTokenByOrgId } from "../middlewares/tokenUtils";
import { performInstantSyncForApi } from "../utils/syncUtils";
import { SyncedEntityType } from "@prisma/client";
import prisma from "../config/db";
import instantSyncCron from "../services/cron/instantSyncCron";
import logger from "../utils/logger";

const router = express.Router();

router.get(
  "/service",
  asyncHandler(async (req, res) => {
    const { entity, connectionId, operation } = req.query;
    if (!entity || !connectionId || !operation) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription:
          "entity, connectionId, and operation parameters are required",
      });
    }
    //operation can be fetch or post
    if (operation !== "fetch" && operation !== "post") {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: "operation must be either 'fetch' or 'post'",
      });
    }

    if (operation === "post") {
      //req.body should contain the service data to be posted
      const serviceData = req.body;
      if (!serviceData) {
        throw new ApiException({
          status: HttpStatus.BAD_REQUEST,
          code: ErrorCode.BAD_REQUEST,
          errorDescription: "Service data is required for posting",
        });
      }
    }

    const serviceResponse = await services.fetchService(
      entity as string,
      connectionId as string
    );

    return serviceResponse;
  })
);

router.get(
  "/sync/history/",
  asyncHandler(async (req, res) => {
    const { page, limit } = req.query;
    const pageNumber = parseInt(page as string) || 1;
    const pageSize = parseInt(limit as string) || 10;
    const zactCompanyId = req.headers.zactcompanyid as string;
    const syncHistory = await services.getSyncHistory(
      pageNumber,
      pageSize,
      zactCompanyId
    );
    return res.status(HttpStatus.OK).json(syncHistory);
  })
);

/**
 * POST /sync/instant/:entityType?batchSize=1000
 *
 * Performs instant sync for a specific entity type or all supported entity types and returns data in simplified Kafka message format.
 * Supports batching for large datasets to prevent memory issues and improve performance.
 *
 * Headers:
 * - zactcompanyid: Required. The organization ID to sync data for.
 *
 * Path Parameters:
 * - entityType: The entity type to sync (ACCOUNT, VENDOR, CLASS, ALL)
 *
 * Query Parameters:
 * - batchSize: Optional. Number of records per batch (default: 1000)
 *
 * Response Format (Single Entity - Small Dataset):
 * {
 *   "data": {
 *     "create": {
 *       "vendor": [...] // Array of newly created vendor records
 *     },
 *     "update": {
 *       "vendor": [...] // Array of updated vendor records
 *     }
 *   },
 *   "message": "Instant sync completed for VENDOR: 5 created, 3 updated",
 *   "summary": {
 *     "created": 5,
 *     "updated": 3,
 *     "total": 8
 *   }
 * }
 *
 * Response Format (Single Entity - Large Dataset with Batching):
 * {
 *   "data": {
 *     "create": {
 *       "vendor": [...] // First batch of newly created vendor records
 *     },
 *     "update": {
 *       "vendor": [...] // First batch of updated vendor records
 *     }
 *   },
 *   "message": "Instant sync completed for VENDOR: 5000 created, 3000 updated (showing batch 1 of 8)",
 *   "summary": {
 *     "created": 5000,
 *     "updated": 3000,
 *     "total": 8000
 *   },
 *   "batches": {
 *     "totalBatches": 8,
 *     "batchSize": 1000,
 *     "currentBatch": 1,
 *     "hasMoreBatches": true
 *   }
 * }
 *
 * Response Format (ALL Entities):
 * {
 *   "data": {
 *     "create": {
 *       "vendor": [...],  // Array of newly created vendor records
 *       "account": [...], // Array of newly created account records
 *       "class": [...]    // Array of newly created class records
 *     },
 *     "update": {
 *       "vendor": [...],  // Array of updated vendor records
 *       "account": [...], // Array of updated account records
 *       "class": [...]    // Array of updated class records
 *     }
 *   },
 *   "message": "Instant sync completed for ALL entities: 15 created, 8 updated across 3 entity types",
 *   "summary": {
 *     "created": 15,
 *     "updated": 8,
 *     "total": 23
 *   },
 *   "details": {
 *     "successful": [
 *       { "entityType": "VENDOR", "summary": { "created": 5, "updated": 3, "total": 8 } }
 *     ],
 *     "failed": [
 *       { "entityType": "CLASS", "error": "Error message" }
 *     ]
 *   }
 * }
 */
router.post(
  "/sync/instant/:entityType",
  asyncHandler(async (req, res) => {
    const { entityType } = req.params;
    const { batchSize } = req.query;
    const zactCompanyId = req.headers.zactcompanyid as string;
    const batchSizeNumber = parseInt(batchSize as string) || 1000;

    // Validate required headers
    if (!zactCompanyId) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: "zactcompanyid header is required",
      });
    }

    // Validate entity type - only support ACCOUNT, VENDOR, CLASS for instant sync
    const supportedEntityTypes = [
      SyncedEntityType.ACCOUNT,
      SyncedEntityType.VENDOR,
      SyncedEntityType.CLASS,
    ];
    const supportedEntityTypeStrings = ["ACCOUNT", "VENDOR", "CLASS"];
    const upperEntityTypeString = entityType.toUpperCase();

    if (
      upperEntityTypeString !== "ALL" &&
      !supportedEntityTypeStrings.includes(upperEntityTypeString)
    ) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        errorDescription: `Invalid entity type. Valid types are: ${supportedEntityTypeStrings.join(
          ", "
        )}, ALL`,
      });
    }

    // Get connection for the organization
    const integration = await prisma.accountingPlatformIntegration.findFirst({
      where: {
        zactCompanyId,
        connectionStatus: "ACTIVE",
      },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        errorDescription:
          "No active accounting platform integration found for this organization",
      });
    }

    // Get valid QBO token
    const { accessToken, realmId } = await getValidQboTokenByOrgId(
      zactCompanyId
    );

    // Handle "ALL" case or single entity type
    if (upperEntityTypeString === "ALL") {
      // Sync all supported entity types
      const allResults = await Promise.allSettled(
        supportedEntityTypes.map(async (entityType) => {
          try {
            const result = await performInstantSyncForApi(
              integration.id,
              entityType,
              accessToken,
              realmId,
              batchSizeNumber
            );
            return { entityType, ...result };
          } catch (error) {
            return {
              entityType,
              error: error instanceof Error ? error.message : "Unknown error",
            };
          }
        })
      );

      // Aggregate results
      const successfulResults = allResults
        .filter((result) => result.status === "fulfilled")
        .map((result) => (result as PromiseFulfilledResult<any>).value)
        .filter((result) => !result.error);

      const failedResults = allResults
        .filter(
          (result) =>
            result.status === "rejected" ||
            (result.status === "fulfilled" &&
              (result as PromiseFulfilledResult<any>).value.error)
        )
        .map((result) => {
          if (result.status === "rejected") {
            return { entityType: "unknown", error: result.reason };
          } else {
            return (result as PromiseFulfilledResult<any>).value;
          }
        });

      // Organize data by entity type (fix redundant nesting)
      const organizedData = {
        create: {
          vendor:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.VENDOR
            )?.data?.create?.vendor || [],
          account:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.ACCOUNT
            )?.data?.create?.account || [],
          class:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.CLASS
            )?.data?.create?.class || [],
        },
        update: {
          vendor:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.VENDOR
            )?.data?.update?.vendor || [],
          account:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.ACCOUNT
            )?.data?.update?.account || [],
          class:
            successfulResults.find(
              (r) => r.entityType === SyncedEntityType.CLASS
            )?.data?.update?.class || [],
        },
      };

      const combinedSummary = successfulResults.reduce(
        (acc, result) => ({
          created: acc.created + (result.summary?.created || 0),
          updated: acc.updated + (result.summary?.updated || 0),
          total: acc.total + (result.summary?.total || 0),
        }),
        { created: 0, updated: 0, total: 0 }
      );

      const result = {
        data: organizedData,
        message: `Instant sync completed for ALL entities: ${combinedSummary.created} created, ${combinedSummary.updated} updated across ${successfulResults.length} entity types`,
        summary: combinedSummary,
        details: {
          successful: successfulResults.map((r) => ({
            entityType: r.entityType,
            summary: r.summary,
          })),
          failed: failedResults.map((r) => ({
            entityType: r.entityType,
            error: r.error,
          })),
        },
      };

      return res.status(HttpStatus.OK).json(result);
    } else {
      // Sync single entity type
      const result = await performInstantSyncForApi(
        integration.id,
        upperEntityTypeString as SyncedEntityType,
        accessToken,
        realmId,
        batchSizeNumber
      );

      return res.status(HttpStatus.OK).json(result);
    }
  })
);

/**
 * POST /sync/cron/trigger
 *
 * Manually trigger the instant sync cron job for testing purposes.
 * This endpoint runs the same logic as the scheduled cron job.
 *
 * Headers:
 * - No special headers required (processes all active connections)
 *
 * Response:
 * {
 *   "message": "Cron job triggered successfully",
 *   "status": "running"
 * }
 */
router.post(
  "/sync/cron/trigger",
  asyncHandler(async (req, res) => {
    // Trigger the cron job manually (don't await to return response immediately)
    instantSyncCron.runIncrementalSyncCron().catch((error: any) => {
      logger.error("Manual cron trigger failed", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
    });

    return res.status(HttpStatus.OK).json({
      message: "Instant sync cron job triggered successfully",
      status: "running",
      note: "Check server logs for detailed progress and results",
    });
  })
);

export default router;
