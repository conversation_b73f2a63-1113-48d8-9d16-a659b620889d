-- CreateTable
CREATE TABLE `accounting_platform_integrations` (
    `connection_id` VARCHAR(191) NOT NULL,
    `zact_company_id` VARCHAR(191) NOT NULL,
    `accounting_Platform_type` VARCHAR(191) NOT NULL,
    `external_company_id` VARCHAR(191) NOT NULL,
    `company_name` VARCHAR(191) NOT NULL,
    `connection_status` ENUM('ACTIVE', 'INACTIVE', 'PENDING', 'ERROR') NOT NULL,
    `last_connected_at` DATETIME(3) NULL,
    `authentication` JSON NULL,
    `scheduled_jobs` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `accounting_platform_integrations_zact_company_id_key`(`zact_company_id`),
    INDEX `accounting_platform_integrations_connection_status_idx`(`connection_status`),
    UNIQUE INDEX `accounting_platform_integrations_accounting_Platform_type_ex_key`(`accounting_Platform_type`, `external_company_id`),
    <PERSON>IMARY KEY (`connection_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `entity_sync_states` (
    `id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `entity_type` ENUM('ACCOUNT', 'BILL', 'PAYMENT', 'CLASS', 'VENDOR', 'JOURNAL_ENTRY') NOT NULL,
    `last_successful_sync_at` DATETIME(3) NULL,
    `last_attempted_sync_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `entity_sync_states_connection_id_idx`(`connection_id`),
    INDEX `entity_sync_states_entity_type_idx`(`entity_type`),
    UNIQUE INDEX `entity_sync_states_connection_id_entity_type_key`(`connection_id`, `entity_type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `request_logs` (
    `log_id` VARCHAR(191) NOT NULL,
    `source_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NULL,
    `log_type` ENUM('API', 'KAFKA') NOT NULL,
    `endpoint_or_topic` VARCHAR(191) NOT NULL,
    `method_or_action` VARCHAR(191) NOT NULL,
    `execution_time` DATETIME(3) NOT NULL,
    `details` JSON NULL,
    `status` INTEGER NOT NULL,
    `message` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `request_logs_source_id_idx`(`source_id`),
    INDEX `request_logs_connection_id_idx`(`connection_id`),
    INDEX `request_logs_log_type_idx`(`log_type`),
    INDEX `request_logs_created_at_idx`(`created_at`),
    PRIMARY KEY (`log_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sync_operations` (
    `sync_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `entity_type` ENUM('ACCOUNT', 'BILL', 'PAYMENT', 'CLASS', 'VENDOR', 'JOURNAL_ENTRY') NOT NULL,
    `sync_type` ENUM('FULL_SYNC', 'INCREMENTAL_SYNC', 'SCHEDULED_SYNC') NOT NULL,
    `sync_started_at` DATETIME(3) NOT NULL,
    `sync_completed_at` DATETIME(3) NULL,
    `status` ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED') NOT NULL,
    `records_processed` INTEGER NOT NULL DEFAULT 0,
    `records_succeeded` INTEGER NOT NULL DEFAULT 0,
    `records_failed` INTEGER NOT NULL DEFAULT 0,
    `error_message` VARCHAR(191) NULL,
    `error_code` INTEGER NULL,
    `retry_count` INTEGER NOT NULL DEFAULT 0,
    `max_retries` INTEGER NOT NULL DEFAULT 3,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `sync_operations_connection_id_idx`(`connection_id`),
    INDEX `sync_operations_entity_type_idx`(`entity_type`),
    INDEX `sync_operations_status_idx`(`status`),
    INDEX `sync_operations_sync_started_at_idx`(`sync_started_at`),
    INDEX `sync_operations_connection_id_entity_type_sync_started_at_idx`(`connection_id`, `entity_type`, `sync_started_at`),
    PRIMARY KEY (`sync_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `accounts` (
    `account_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `external_account_id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `fully_qualified_name` VARCHAR(191) NOT NULL,
    `account_type` VARCHAR(191) NOT NULL,
    `account_sub_type` VARCHAR(191) NULL,
    `category` VARCHAR(191) NULL,
    `parent_id` VARCHAR(191) NULL,
    `active` BOOLEAN NOT NULL,
    `current_balance` DOUBLE NOT NULL,
    `sub_account` BOOLEAN NOT NULL,
    `current_balance_with_sub_accounts` DOUBLE NULL,
    `currency_code` VARCHAR(191) NULL,
    `account_number` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `bank_account_number` VARCHAR(191) NULL,
    `routing_number` VARCHAR(191) NULL,
    `tax_type` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `domain` VARCHAR(191) NOT NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NOT NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `accounts_connection_id_idx`(`connection_id`),
    INDEX `accounts_active_idx`(`active`),
    INDEX `accounts_name_idx`(`name`),
    UNIQUE INDEX `accounts_connection_id_external_account_id_key`(`connection_id`, `external_account_id`),
    PRIMARY KEY (`account_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vendors` (
    `vendor_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `external_vendor_id` VARCHAR(191) NULL,
    `vendor_name` VARCHAR(191) NOT NULL,
    `contact_name` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `phone` VARCHAR(191) NULL,
    `website` VARCHAR(191) NULL,
    `bank_account_number` VARCHAR(191) NULL,
    `addresses` JSON NULL,
    `is_active` BOOLEAN NOT NULL,
    `domain` VARCHAR(191) NOT NULL,
    `balance` DOUBLE NULL,
    `tax_number` VARCHAR(191) NULL,
    `currency_code` VARCHAR(191) NULL,
    `payment_terms_id` VARCHAR(191) NULL,
    `default_expense_account_id` VARCHAR(191) NULL,
    `default_bill_payment_account_id` VARCHAR(191) NULL,
    `vendor_number` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `vendors_connection_id_idx`(`connection_id`),
    INDEX `vendors_is_active_idx`(`is_active`),
    INDEX `vendors_vendor_name_idx`(`vendor_name`),
    UNIQUE INDEX `vendors_connection_id_external_vendor_id_key`(`connection_id`, `external_vendor_id`),
    PRIMARY KEY (`vendor_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `bills` (
    `bill_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `vendor_id` VARCHAR(191) NOT NULL,
    `external_bill_id` VARCHAR(191) NULL,
    `bill_number` VARCHAR(191) NOT NULL,
    `bill_date` DATETIME(3) NOT NULL,
    `due_date` DATETIME(3) NOT NULL,
    `total_amount` DOUBLE NOT NULL,
    `balance` DOUBLE NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `status` ENUM('DRAFT', 'OPEN', 'PAID', 'VOID') NOT NULL,
    `private_note` VARCHAR(191) NULL,
    `exchange_rate` DOUBLE NULL,
    `sub_total` DOUBLE NULL,
    `tax_amount` DOUBLE NULL,
    `discount_amount` DOUBLE NULL,
    `payment_terms_id` VARCHAR(191) NULL,
    `reference_number` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `line_items` JSON NOT NULL,
    `account_id` VARCHAR(191) NULL,
    `class_id` VARCHAR(191) NULL,
    `domain` VARCHAR(191) NOT NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `bills_connection_id_idx`(`connection_id`),
    INDEX `bills_vendor_id_idx`(`vendor_id`),
    INDEX `bills_status_idx`(`status`),
    INDEX `bills_bill_date_idx`(`bill_date`),
    UNIQUE INDEX `bills_connection_id_external_bill_id_key`(`connection_id`, `external_bill_id`),
    PRIMARY KEY (`bill_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `classes` (
    `class_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `external_class_id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `has_children` BOOLEAN NOT NULL DEFAULT false,
    `parent_id` VARCHAR(191) NULL,
    `is_active` BOOLEAN NOT NULL,
    `fully_qualified_name` VARCHAR(191) NULL,
    `class_number` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `domain` VARCHAR(191) NOT NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `classes_connection_id_idx`(`connection_id`),
    INDEX `classes_is_active_idx`(`is_active`),
    INDEX `classes_name_idx`(`name`),
    UNIQUE INDEX `classes_connection_id_external_class_id_key`(`connection_id`, `external_class_id`),
    PRIMARY KEY (`class_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `journal_entries` (
    `journal_entry_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `external_journal_entry_id` VARCHAR(191) NULL,
    `transaction_date` DATETIME(3) NOT NULL,
    `currency_code` VARCHAR(191) NULL,
    `total_amount` DOUBLE NULL,
    `memo` VARCHAR(191) NULL,
    `exchange_rate` DOUBLE NULL,
    `journal_number` VARCHAR(191) NULL,
    `posting_status` VARCHAR(191) NULL,
    `accounting_period_id` VARCHAR(191) NULL,
    `inclusive_of_tax` BOOLEAN NULL,
    `company_id` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `line_items` JSON NOT NULL,
    `domain` VARCHAR(191) NOT NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `journal_entries_connection_id_idx`(`connection_id`),
    INDEX `journal_entries_transaction_date_idx`(`transaction_date`),
    UNIQUE INDEX `journal_entries_connection_id_external_journal_entry_id_key`(`connection_id`, `external_journal_entry_id`),
    PRIMARY KEY (`journal_entry_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `payments` (
    `payment_id` VARCHAR(191) NOT NULL,
    `connection_id` VARCHAR(191) NOT NULL,
    `vendor_id` VARCHAR(191) NOT NULL,
    `external_payment_id` VARCHAR(191) NULL,
    `payment_type` ENUM('CHECK', 'CREDIT_CARD', 'CASH') NOT NULL,
    `total_amount` DOUBLE NOT NULL,
    `payment_date` DATETIME(3) NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `bank_account_id` VARCHAR(191) NULL,
    `credit_card_account_id` VARCHAR(191) NULL,
    `check_number` VARCHAR(191) NULL,
    `private_note` VARCHAR(191) NULL,
    `reference_number` VARCHAR(191) NULL,
    `exchange_rate` DOUBLE NULL,
    `payment_method_id` VARCHAR(191) NULL,
    `memo` VARCHAR(191) NULL,
    `external_id` VARCHAR(191) NULL,
    `platform_url` VARCHAR(191) NULL,
    `bill_payments` JSON NOT NULL,
    `domain` VARCHAR(191) NOT NULL,
    `created_at_platform` DATETIME(3) NOT NULL,
    `updated_at_platform` DATETIME(3) NULL,
    `last_synced_at` DATETIME(3) NOT NULL,

    INDEX `payments_connection_id_idx`(`connection_id`),
    INDEX `payments_vendor_id_idx`(`vendor_id`),
    INDEX `payments_payment_type_idx`(`payment_type`),
    INDEX `payments_payment_date_idx`(`payment_date`),
    UNIQUE INDEX `payments_connection_id_external_payment_id_key`(`connection_id`, `external_payment_id`),
    PRIMARY KEY (`payment_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `entity_sync_states` ADD CONSTRAINT `entity_sync_states_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `request_logs` ADD CONSTRAINT `request_logs_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sync_operations` ADD CONSTRAINT `sync_operations_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `accounts` ADD CONSTRAINT `accounts_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `vendors` ADD CONSTRAINT `vendors_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `bills` ADD CONSTRAINT `bills_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `classes` ADD CONSTRAINT `classes_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `journal_entries` ADD CONSTRAINT `journal_entries_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `payments` ADD CONSTRAINT `payments_connection_id_fkey` FOREIGN KEY (`connection_id`) REFERENCES `accounting_platform_integrations`(`connection_id`) ON DELETE CASCADE ON UPDATE CASCADE;
