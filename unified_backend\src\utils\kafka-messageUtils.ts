import { v4 as uuidv4 } from "uuid";
import {
  BaseMessage,
  EntityOperation,
  EntityType,
  ERPSystem,
  MessageDestination,
  MessageSource,
  MessageType,
  OperationType,
  Pagination,
  RequestMessage,
} from "../interfaces/kafkaMessageInterface";

/**
 * Creates a base message with common fields
 */
export const createBaseMessage = <T extends MessageType>(
  source: MessageSource,
  destination: MessageDestination,
  messageType: T,
  erpSystem: ERPSystem,
  entityOperation: EntityOperation,
  correlationId: string = uuidv4()
): BaseMessage => {
  return {
    messageId: uuidv4(),
    correlationId,
    timestamp: Date.now(),
    source,
    destination,
    messageType,
    erpSystem,
    entityOperation,
    filters: null,
    metadata: {
      processingTimeMs: null,
      errorType: null,
      errorDetails: null,
    },
    securityContext: {
      connectionId: "",
    },
  };
};

/**
 * Maps a message source to its corresponding destination
 */
export const mapSourceToDestination = (
  source: MessageSource
): MessageDestination => {
  switch (source) {
    case MessageSource.ZACT_APP:
      return MessageDestination.ZACT_APP;
    case MessageSource.UNIFIED_BACKEND:
      return MessageDestination.UNIFIED_BACKEND;
    case MessageSource.ERP_SERVICE:
      return MessageDestination.ERP_SERVICE;
    default:
      return MessageDestination.ZACT_APP; // Default fallback
  }
};

/**
 * Creates an info message with processed counts after sync completion
 */
export const createSyncInfoMessage = (
  correlationId: string,
  connectionId: string,
  processedCounts: Record<string, number>,
  erpSystem: ERPSystem = ERPSystem.QBO,
  processingTimeMs?: number
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE,
    erpSystem,
    {
      entityType: EntityType.ALL, // Indicates sync completion for all entity types
      operation: OperationType.SYNC,
    },
    correlationId
  );

  const totalRecords = Object.values(processedCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      data: processedCounts,
      message:
        `Sync completed successfully. Processing ${totalRecords} records across ${
          Object.keys(processedCounts).length
        } entity types.` || "Successfully processed counts.",
      // Custom field for processed counts
    },
    status: {
      code: "200",
      message: "Sync info",
    },
    metadata: {
      ...baseMessage.metadata,
      processingTimeMs: processingTimeMs || null,
    },
  };
};

/**
 * Creates an error message
 */
export const createErrorMessage = (
  correlationId: string,
  connectionId: string,
  errorType: string,
  errorDetails: string,
  entityType?: EntityType,
  erpSystem: ERPSystem = ERPSystem.QBO,
  processingTimeMs?: number
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE,
    erpSystem,
    {
      entityType: entityType || EntityType.ALL, // Use ALL for general sync errors
      operation: OperationType.SYNC,
    },
    correlationId
  );

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      data: [],
      message: `Error during sync: ${errorDetails}`,
    },
    status: {
      code: "500",
      message: "Sync error",
    },
    metadata: {
      processingTimeMs: processingTimeMs || null,
      errorType,
      errorDetails,
    },
  };
};

/**
 * Creates a data batch message for entity data
 */
export const createEntityDataBatchMessage = (
  correlationId: string,
  connectionId: string,
  entityType: EntityType,
  data: any[],
  batchNumber: number,
  totalBatches: number,
  batchSize: number = 1000,
  erpSystem: ERPSystem = ERPSystem.QBO
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE,
    erpSystem,
    {
      entityType,
      operation: OperationType.FETCH,
    },
    correlationId
  );

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      data,
      entityType,
      batchNumber,
      totalBatches,
      batchSize,
    },
    status: {
      code: "200",
      message: `Data batch ${batchNumber}/${totalBatches} for ${entityType}`,
    },
  };
};

/**
 * Creates a response message (keeping original for backward compatibility)
 */
export const createResponseMessage = (
  requestMessage: RequestMessage | null,
  data: any[] = [],
  pagination?: Pagination,
  processingTimeMs: number = 0,
  statusCode: string = "200",
  statusMessage?: string
): any => {
  const timestamp = Date.now();
  const startTime = requestMessage
    ? requestMessage.timestamp
    : timestamp - processingTimeMs;

  const baseMessage = requestMessage
    ? {
        messageId: uuidv4(),
        correlationId: requestMessage.correlationId,
        timestamp,
        source: MessageSource.UNIFIED_BACKEND,
        destination: mapSourceToDestination(requestMessage.source),
        messageType: MessageType.RESPONSE as MessageType.RESPONSE,
        erpSystem: requestMessage.erpSystem,
        entityOperation: requestMessage.entityOperation,
        filters: null,
        metadata: {
          processingTimeMs: timestamp - startTime,
          errorType: null,
          errorDetails: null,
        },
        securityContext: requestMessage.securityContext,
      }
    : {
        ...createBaseMessage(
          MessageSource.UNIFIED_BACKEND,
          MessageDestination.ZACT_APP,
          MessageType.RESPONSE as MessageType.RESPONSE,
          ERPSystem.QBO,
          { entityType: EntityType.ACCOUNT, operation: OperationType.FETCH }
        ),
      };

  const message =
    statusMessage || `Successfully fetched ${data.length} records`;

  return {
    ...baseMessage,
    payload: {
      data,
      ...(pagination && { pagination }),
    },
    status: {
      code: statusCode,
      message,
    },
  };
};

/**
 * Creates a sync completion message (keeping original for backward compatibility)
 */
export const createSyncCompletionMessage = (
  correlationId: string,
  connectionId: string,
  entityCounts: Record<string, number>,
  erpSystem: ERPSystem = ERPSystem.QBO
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE as MessageType.RESPONSE,
    erpSystem,
    {
      entityType: EntityType.ALL, // Indicates sync completion for all entity types
      operation: OperationType.SYNC,
    },
    correlationId
  );

  const totalCount = Object.values(entityCounts).reduce(
    (sum, count) => sum + count,
    0
  );

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      entityCounts,
      message: `Initial sync completed with ${totalCount} records. Data stream active...`,
    },
    status: {
      code: "200",
      message: "Initial sync completed successfully",
    },
  };
};

/**
 * Creates an entity batch message (keeping original for backward compatibility)
 */
export const createEntityBatchMessage = (
  correlationId: string,
  connectionId: string,
  entityType: EntityType,
  data: any[],
  batchNumber: number,
  totalBatches: number,
  erpSystem: ERPSystem = ERPSystem.QBO
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE as MessageType.RESPONSE,
    erpSystem,
    {
      entityType,
      operation: OperationType.FETCH,
    },
    correlationId
  );

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      data,
      entityType,
      batchNumber,
      totalBatches,
    },
    status: {
      code: "200",
      message: `Successfully fetched ${entityType} batch ${batchNumber}/${totalBatches}`,
    },
  };
};

/**
 * Creates an instant sync message with create/update differentiation
 */
export const createInstantSyncMessage = (
  correlationId: string,
  connectionId: string,
  entityType: EntityType,
  createdRecords: any[],
  updatedRecords: any[],
  batchNumber: number,
  totalBatches: number,
  erpSystem: ERPSystem = ERPSystem.QBO
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE as MessageType.RESPONSE,
    erpSystem,
    {
      entityType,
      operation: OperationType.SYNC,
    },
    correlationId
  );

  const totalRecords = createdRecords.length + updatedRecords.length;

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      create: createdRecords,
      update: updatedRecords,
      entityType,
      batchNumber,
      totalBatches,
      batchSize: totalRecords,
      summary: {
        created: createdRecords.length,
        updated: updatedRecords.length,
        total: totalRecords,
      },
      syncType: "INCREMENTAL_SYNC",
    },
    status: {
      code: "200",
      message: `Incremental sync batch ${batchNumber}/${totalBatches} for ${entityType}: ${createdRecords.length} created, ${updatedRecords.length} updated`,
    },
  };
};

/**
 * Creates a scheduled sync message with create/update differentiation
 */
export const createScheduledSyncMessage = (
  correlationId: string,
  connectionId: string,
  entityType: EntityType,
  createdRecords: any[],
  updatedRecords: any[],
  batchNumber: number,
  totalBatches: number,
  erpSystem: ERPSystem = ERPSystem.QBO
): any => {
  const baseMessage = createBaseMessage(
    MessageSource.UNIFIED_BACKEND,
    MessageDestination.ZACT_APP,
    MessageType.RESPONSE as MessageType.RESPONSE,
    erpSystem,
    {
      entityType,
      operation: OperationType.SYNC,
    },
    correlationId
  );

  const totalRecords = createdRecords.length + updatedRecords.length;

  return {
    ...baseMessage,
    securityContext: {
      connectionId,
    },
    payload: {
      create: createdRecords,
      update: updatedRecords,
      entityType,
      batchNumber,
      totalBatches,
      batchSize: totalRecords,
      summary: {
        created: createdRecords.length,
        updated: updatedRecords.length,
        total: totalRecords,
      },
      syncType: "SCHEDULED_SYNC",
    },
    status: {
      code: "200",
      message: `Scheduled sync batch ${batchNumber}/${totalBatches} for ${entityType}: ${createdRecords.length} created, ${updatedRecords.length} updated`,
    },
  };
};
