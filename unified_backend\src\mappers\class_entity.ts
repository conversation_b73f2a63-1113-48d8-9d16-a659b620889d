export interface IQboClassResponse {
  Id: string;
  Name: string;
  Active: boolean;
  SubClass: boolean;
  FullyQualifiedName?: string;
  ParentRef?: {
    value: string;
  };
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
  domain: string;
}

export interface IUnifiedClass {
  id: string;
  name: string; // MANDATORY - Required by ALL platforms
  status: boolean; // MANDATORY - Required by ALL platforms
  hasChildren: boolean;
  parentId: string | null;
  // Optional fields - Platform specific requirements
  fullyQualifiedName?: string | null; // Required by: QBO, QBD, Merge, Rutter (not Xero, NetSuite)
  classNumber?: string | null; // Required by: QBD, NetSuite only
  description?: string | null; // Required by: NetSuite, Merge only
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  createdAtPlatform: Date;
  updatedAtPlatform: Date;
  domain: string;
}

export const mapQboClassResponseToUnified = (
  response: IQboClassResponse
): IUnifiedClass => {
  return {
    id: response.Id,
    name: response.Name,
    status: response.Active,
    hasChildren: response.SubClass,
    parentId: response.ParentRef?.value ?? null,
    createdAtPlatform: response.MetaData?.CreateTime
      ? new Date(response.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: response.MetaData?.LastUpdatedTime
      ? new Date(response.MetaData.LastUpdatedTime)
      : new Date(),
    domain: response.domain,
  };
};
