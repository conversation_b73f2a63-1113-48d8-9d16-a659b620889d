# Server Configuration
PORT=3001
NODE_ENV=development

# QBO Configuration
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_REDIRECT_URI=http://localhost:4200/auth/callback
QBO_ENVIRONMENT=sandbox
QBO_AUTH_URL=https://appcenter.intuit.com/connect/oauth2
QBO_TOKEN_URL=https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
QBO_REVOKE_URL=https://developer.api.intuit.com/v2/oauth2/tokens/revoke
QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com/v3/company
QBO_API_BASE_URL=https://quickbooks.api.intuit.com/v3/company

# Unified Backend Configuration
UB_BASE_URL=http://localhost:8080
UB_API_KEY=your_ub_api_key
UB_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=10m
LOG_MAX_FILES=7

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:4200

# API Configuration
API_TIMEOUT=30000
