# 🐳 Docker Setup Guide - Zact Unified Platform

## 📋 Overview

This guide covers the complete Docker setup for the Zact Unified Platform, which consists of:

- **Auth Gateway**: OAuth 2.0 authentication microservice (Port 3001)
- **Unified Backend**: Main business logic service (Port 8080)
- **MySQL**: Database (Port 3306/3307)
- **Kafka + Zookeeper**: Message broker (Port 9092/9093)
- **Kafka UI**: Monitoring interface (Port 8090/8091)

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.docker .env

# Edit environment variables
nano .env  # or use your preferred editor
```

### 2. Production Deployment

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 3. Development Environment

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

## 📁 Project Structure

```
zact-unified-platform/
├── auth-gateway/                 # Auth Gateway Microservice
│   ├── Dockerfile               # Production build
│   ├── Dockerfile.dev           # Development build
│   └── .dockerignore
├── unified_backend/              # Unified Backend Service
│   ├── Dockerfile               # Production build
│   ├── Dockerfile.dev           # Development build
│   └── .dockerignore
├── docker-compose.yml           # Production configuration
├── docker-compose.dev.yml       # Development configuration
├── .env.example                 # Environment template
├── .env.docker                  # Docker-specific template
└── DOCKER_SETUP.md             # This file
```

## 🔧 Configuration

### Environment Variables

| Variable               | Description                     | Default         |
| ---------------------- | ------------------------------- | --------------- |
| `AUTH_GATEWAY_PORT`    | Auth Gateway port               | 3001            |
| `UNIFIED_BACKEND_PORT` | Unified Backend port            | 8080            |
| `QBO_CLIENT_ID`        | QuickBooks Online Client ID     | Required        |
| `QBO_CLIENT_SECRET`    | QuickBooks Online Client Secret | Required        |
| `DATABASE_URL`         | MySQL connection string         | Auto-configured |
| `KAFKA_BROKERS`        | Kafka broker addresses          | kafka:29092     |

### Service Dependencies

```mermaid
graph TD
    A[Frontend] --> B[Auth Gateway]
    B --> C[Unified Backend]
    C --> D[MySQL Database]
    C --> E[Kafka]
    E --> F[Zookeeper]
    G[Kafka UI] --> E
```

## 🛠️ Development Workflow

### Hot Reload Development

```bash
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# Attach to service logs
docker-compose -f docker-compose.dev.yml logs -f auth-gateway-dev
docker-compose -f docker-compose.dev.yml logs -f unified-backend-dev
```

### Debugging

- **Auth Gateway Debug Port**: 9230
- **Unified Backend Debug Port**: 9229

```bash
# Connect debugger to running containers
# VS Code: Add to launch.json
{
  "type": "node",
  "request": "attach",
  "name": "Docker: Auth Gateway",
  "remoteRoot": "/app",
  "localRoot": "${workspaceFolder}/auth-gateway",
  "port": 9230
}
```

## 📊 Monitoring & Health Checks

### Service Health

```bash
# Check service health
curl http://localhost:3001/health  # Auth Gateway
curl http://localhost:8080/health  # Unified Backend
```

### Kafka Monitoring

- **Kafka UI**: http://localhost:8090 (production) or http://localhost:8091 (development)

### Database Access

```bash
# Connect to MySQL
docker exec -it zact-mysql mysql -u zact_user -p zact_unified
```

## 🔄 Common Commands

### Service Management

```bash
# Restart specific service
docker-compose restart auth-gateway
docker-compose restart unified-backend

# Rebuild and restart
docker-compose up -d --build auth-gateway

# Scale services (if needed)
docker-compose up -d --scale unified-backend=2
```

### Data Management

```bash
# Backup database
docker exec zact-mysql mysqldump -u root -p zact_unified > backup.sql

# Reset volumes (WARNING: Data loss)
docker-compose down -v
docker volume prune
```

### Logs and Debugging

```bash
# Follow logs for all services
docker-compose logs -f

# Follow logs for specific service
docker-compose logs -f unified-backend

# Execute commands in running container
docker exec -it zact-unified-backend sh
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**

   ```bash
   # Check port usage
   netstat -tulpn | grep :8080

   # Change ports in .env file
   UNIFIED_BACKEND_PORT=8081
   ```

2. **Database Connection Issues**

   ```bash
   # Check MySQL health
   docker-compose logs mysql

   # Verify database exists
   docker exec -it zact-mysql mysql -u root -p -e "SHOW DATABASES;"
   ```

3. **Kafka Connection Issues**

   ```bash
   # Check Kafka health
   docker-compose logs kafka

   # Verify topics
   docker exec -it zact-kafka kafka-topics --bootstrap-server localhost:9092 --list
   ```

### Performance Optimization

1. **Memory Allocation**

   ```yaml
   # Add to docker-compose.yml
   services:
     unified-backend:
       deploy:
         resources:
           limits:
             memory: 1G
           reservations:
             memory: 512M
   ```

2. **Volume Optimization**
   ```bash
   # Use bind mounts for development
   volumes:
     - ./unified_backend:/app:cached
   ```

## 🔒 Security Considerations

### Production Security

- Change default passwords in `.env`
- Use secrets management for sensitive data
- Enable SSL/TLS for external connections
- Implement network policies

### Development Security

- Use separate development credentials
- Avoid exposing production ports
- Regular security updates

## 📈 Scaling & Production

### Horizontal Scaling

```yaml
# docker-compose.prod.yml
services:
  unified-backend:
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
```

### Load Balancing

- Add nginx reverse proxy
- Configure health checks
- Implement circuit breakers

## 📋 Quick Reference

### Essential Commands

```bash
# Production
docker-compose up -d                    # Start all services
docker-compose down                     # Stop all services
docker-compose logs -f                  # View logs
docker-compose ps                       # Check status

# Development
docker-compose -f docker-compose.dev.yml up -d    # Start dev environment
docker-compose -f docker-compose.dev.yml down     # Stop dev environment

# Maintenance
docker-compose restart <service>        # Restart specific service
docker-compose up -d --build           # Rebuild and restart
docker system prune                    # Clean up unused resources
```

## 🆘 Support

For issues and questions:

1. Check logs: `docker-compose logs`
2. Verify configuration: `.env` file
3. Check service health endpoints
4. Review this documentation

---

**Last Updated**: 2025-07-31
**Version**: 1.0.0
