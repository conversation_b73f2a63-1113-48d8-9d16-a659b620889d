import { AuthUrlResponse, TokenResponse, RevokeResponse } from "./auth";

/**
 * Base interface for all authentication providers
 */
export interface AuthProvider {
  /**
   * Generate authentication URL for the provider
   * @param orgId - Organization ID
   * @returns Promise with auth URL response
   */
  generateAuthUrl(orgId: string): Promise<AuthUrlResponse>;

  /**
   * Exchange authorization code for access token
   * @param code - Authorization code
   * @param realmId - Provider-specific company/realm ID
   * @param state - State parameter (usually orgId)
   * @returns Promise with token response
   */
  exchangeCodeForToken(
    code: string,
    realmId: string,
    state: string
  ): Promise<TokenResponse>;

  /**
   * Refresh access token using refresh token
   * @param connectionId - Connection ID from UB
   * @returns Promise with token response
   */
  refreshToken(connectionId: string): Promise<TokenResponse>;

  /**
   * Revoke access token and disconnect
   * @param connectionId - Connection ID from UB
   * @returns Promise with revoke response
   */
  revokeToken(connectionId: string): Promise<RevokeResponse>;
}

/**
 * Provider types supported by the system
 */
export type ProviderType = "qbo" | "xero" | "netsuite" | "qbd";

/**
 * Provider factory interface
 */
export interface ProviderFactory {
  createProvider(type: ProviderType): AuthProvider;
  getSupportedProviders(): ProviderType[];
}
