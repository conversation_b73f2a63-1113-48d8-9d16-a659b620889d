import express from "express";
import asyncHandler from "../utils/async-handler";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";
import { authGatewayServices } from "../services/authGateway"

const router = express.Router();

/**
 * Store authentication tokens from Auth Gateway
 * POST /auth/store-tokens
 */
router.post(
  "/store-tokens",
  asyncHandler(async (req, res) => {
    const {
      orgId,
      realmId,
      companyName,
      accessToken,
      refreshToken,
      expiresAt,
      erpSystem,
    } = req.body;

    // Validate required fields
    if (
      !orgId ||
      !realmId ||
      !companyName ||
      !accessToken ||
      !refreshToken ||
      !expiresAt ||
      !erpSystem
    ) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription:
          "Missing required fields: orgId, realmId, companyName, accessToken, refreshToken, expiresAt, erpSystem",
      });
    }

    // Validate ERP system
    if (erpSystem !== "qbo") {
      throw new ApiException({
        ...ErrorCodes.UNPROCESSABLE_ENTITY,
        errorDescription: "Unsupported ERP system. Only 'qbo' is supported",
      });
    }

    const result = await authGatewayServices.storeTokens({
      orgId,
      realmId,
      companyName,
      accessToken,
      refreshToken,
      expiresAt,
      erpSystem,
    });

    res.json(result);
  })
);

/**
 * Get authentication tokens for Auth Gateway
 * GET /auth/get-tokens/:connectionId
 */
router.get(
  "/get-tokens/:connectionId",
  asyncHandler(async (req, res) => {
    const { connectionId } = req.params;

    if (!connectionId) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "connectionId parameter is required",
      });
    }

    const result = await authGatewayServices.getTokens(connectionId);

    res.json(result);
  })
);

/**
 * Update authentication tokens for Auth Gateway
 * PUT /auth/update-tokens
 */
router.put(
  "/update-tokens",
  asyncHandler(async (req, res) => {
    const { connectionId, accessToken, refreshToken, expiresAt } = req.body;

    if (!connectionId || !accessToken || !refreshToken || !expiresAt) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription:
          "connectionId, accessToken, refreshToken, and expiresAt are required",
      });
    }

    const result = await authGatewayServices.updateTokens({
      connectionId,
      accessToken,
      refreshToken,
      expiresAt,
    });

    res.json(result);
  })
);

/**
 * Delete connection for Auth Gateway
 * DELETE /auth/delete-connection/:connectionId
 */
router.delete(
  "/delete-connection/:connectionId",
  asyncHandler(async (req, res) => {
    const { connectionId } = req.params;

    if (!connectionId) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "connectionId parameter is required",
      });
    }

    const result = await authGatewayServices.deleteConnection(connectionId);

    res.json(result);
  })
);

/**
 * Health check for auth gateway integration
 * GET /auth/health
 */
router.get(
  "/health",
  asyncHandler(async (req, res) => {
    res.json({
      message: "Auth Gateway integration is healthy",
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
