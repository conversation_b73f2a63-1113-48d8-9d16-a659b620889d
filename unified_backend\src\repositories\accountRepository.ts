import prisma from "../config/db";
import logger from "../utils/logger";

/**
 * Repository for Account database operations
 */
export class AccountRepository {
  /**
   * Find accounts by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.account.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find account by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.account.findFirst({
      where: {
        externalAccountId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Create or update account
   */
  static async upsert(accountData: any) {
    return await prisma.account.upsert({
      where: {
        connectionId_externalAccountId: {
          connectionId: accountData.connectionId,
          externalAccountId: accountData.externalAccountId,
        },
      },
      update: {
        ...accountData,
        lastSyncedAt: new Date(),
      },
      create: {
        ...accountData,
        lastSyncedAt: new Date(),
      },
    });
  }

  /**
   * Bulk create accounts
   */
  static async createMany(accounts: any[]) {
    return await prisma.account.createMany({
      data: accounts,
      skipDuplicates: true,
    });
  }

  /**
   * Update account
   */
  static async update(id: string, data: any) {
    return await prisma.account.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old accounts
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.account.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }

  /**
   * Get count by connection ID
   */
  static async countByConnectionId(connectionId: string) {
    return await prisma.account.count({
      where: { connectionId },
    });
  }
}
