version: '3.8'

services:
  # Auth Gateway Service
  auth-gateway:
    build:
      context: ./auth-gateway
      dockerfile: Dockerfile
    container_name: zact-auth-gateway
    restart: unless-stopped
    ports:
      - "${AUTH_GATEWAY_PORT:-3001}:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - UB_BASE_URL=http://unified-backend:8080
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - QBO_REDIRECT_URI=${QBO_REDIRECT_URI}
      - QBO_ENVIRONMENT=${QBO_ENVIRONMENT:-sandbox}
      - UB_API_KEY=${UB_API_KEY}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:4200}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./auth-gateway/logs:/app/logs
    depends_on:
      - unified-backend
    networks:
      - zact-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Unified Backend Service
  unified-backend:
    build:
      context: ./unified_backend
      dockerfile: Dockerfile
    container_name: zact-unified-backend
    restart: unless-stopped
    ports:
      - "${UNIFIED_BACKEND_PORT:-8080}:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DATABASE_URL=${DATABASE_URL}
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=${KAFKA_CLIENT_ID}
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - API_GATEWAY_URL=${API_GATEWAY_URL}
      - JWT_SECRET=${JWT_SECRET}
      - INCREMENTAL_SYNC_CRON_ENABLED=${INCREMENTAL_SYNC_CRON_ENABLED:-false}
      - AUTH_GATEWAY_URL=http://auth-gateway:3001
      - AUTH_GATEWAY_API_KEY=${AUTH_GATEWAY_API_KEY}
    volumes:
      - ./unified_backend/logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      kafka:
        condition: service_healthy
    networks:
      - zact-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: zact-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-zact_unified}
      MYSQL_USER: ${MYSQL_USER:-zact_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-zact_password}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./infrastructure/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - zact-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-rootpassword}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zact-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - zact-network

  # Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: zact-kafka
    restart: unless-stopped
    depends_on:
      - zookeeper
    ports:
      - "${KAFKA_PORT:-9092}:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_MESSAGE_MAX_BYTES: 1000000
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - zact-network
    healthcheck:
      test: ["CMD-SHELL", "kafka-broker-api-versions --bootstrap-server localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka UI for monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: zact-kafka-ui
    restart: unless-stopped
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "${KAFKA_UI_PORT:-8090}:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: zact-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
      DYNAMIC_CONFIG_ENABLED: 'true'
      LOGGING_LEVEL_ROOT: INFO
    networks:
      - zact-network

# Networks
networks:
  zact-network:
    driver: bridge
    name: zact-network

# Volumes
volumes:
  mysql_data:
    name: zact-mysql-data
  kafka_data:
    name: zact-kafka-data
  zookeeper_data:
    name: zact-zookeeper-data
  zookeeper_logs:
    name: zact-zookeeper-logs
