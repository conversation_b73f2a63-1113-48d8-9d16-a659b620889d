# PowerShell script to create all Kafka topics used by the Zact Unified Backend
# This ensures all topics are visible in Kafka UI even before messages are sent

Write-Host "🚀 Creating Kafka topics for Zact Unified Backend..." -ForegroundColor Green

# Kafka broker address
$KAFKA_BROKER = "localhost:9092"

# Wait for Kaf<PERSON> to be ready
Write-Host "⏳ Waiting for Kafka to be ready..." -ForegroundColor Yellow
$timeout = 60
$elapsed = 0
$ready = $false

while ($elapsed -lt $timeout -and -not $ready) {
    try {
        $result = docker exec zact-kafka-local kafka-topics --bootstrap-server $KAFKA_BROKER --list 2>$null
        if ($LASTEXITCODE -eq 0) {
            $ready = $true
        }
    }
    catch {
        # Continue waiting
    }
    
    if (-not $ready) {
        Start-Sleep -Seconds 2
        $elapsed += 2
    }
}

if (-not $ready) {
    Write-Host "❌ Kafka is not ready after $timeout seconds. Please check your Kafka setup." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Kafka is ready!" -ForegroundColor Green

# Function to create topic if it doesn't exist
function Create-Topic {
    param(
        [string]$TopicName,
        [int]$Partitions = 3,
        [int]$ReplicationFactor = 1
    )
    
    Write-Host "📝 Creating topic: $TopicName" -ForegroundColor Cyan
    
    # Check if topic already exists
    $existingTopics = docker exec zact-kafka-local kafka-topics --bootstrap-server $KAFKA_BROKER --list 2>$null
    if ($existingTopics -contains $TopicName) {
        Write-Host "   ℹ️  Topic $TopicName already exists" -ForegroundColor Yellow
    }
    else {
        $result = docker exec zact-kafka-local kafka-topics --bootstrap-server $KAFKA_BROKER --create --topic $TopicName --partitions $Partitions --replication-factor $ReplicationFactor 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Topic $TopicName created successfully" -ForegroundColor Green
        }
        else {
            Write-Host "   ❌ Failed to create topic $TopicName" -ForegroundColor Red
        }
    }
}

# Main application topics (from kafkaTopics.ts)
Write-Host "📋 Creating main application topics..." -ForegroundColor Blue
Create-Topic "entity-create-request" 3 1
Create-Topic "entity-create-response" 3 1
Create-Topic "entity-batch-stream" 3 1
Create-Topic "sync-info" 3 1

# Accounting platform producer topics (from accounting-platform-producer.ts)
Write-Host "📋 Creating accounting platform topics..." -ForegroundColor Blue
Create-Topic "unified-backend-sync-completed" 3 1
Create-Topic "unified-backend-sync-failed" 3 1
Create-Topic "unified-backend-connection-status-changed" 3 1

# List all topics to verify
Write-Host "📋 Current topics in Kafka:" -ForegroundColor Blue
docker exec zact-kafka-local kafka-topics --bootstrap-server $KAFKA_BROKER --list

Write-Host "🎉 Topic creation completed!" -ForegroundColor Green
Write-Host "💡 You can now view all topics in Kafka UI at http://localhost:8090" -ForegroundColor Cyan
