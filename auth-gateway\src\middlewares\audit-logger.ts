import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import logger from "../utils/logger";

// Extend Request interface to include requestId
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
    }
  }
}

/**
 * Request ID middleware - adds unique ID to each request
 */
export const requestIdMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  req.requestId = uuidv4();
  res.setHeader("X-Request-ID", req.requestId);
  next();
};

/**
 * Audit logging middleware for SOC 2 compliance
 * Logs all authentication-related activities
 */
export const auditLogger = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const startTime = Date.now();

  // Log incoming request for audit trail
  const auditData = {
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
    event: "AUTH_REQUEST",
    method: req.method,
    url: req.originalUrl,
    provider: req.get("x-provider"),
    orgId: req.get("zactcompanyid"),
    action: req.body?.action,
    userAgent: req.get("User-Agent"),
    ip: req.ip,
    forwardedFor: req.get("X-Forwarded-For"),
  };

  logger.info("AUDIT: Authentication request received", auditData);

  // Override res.json to log response for audit trail
  const originalJson = res.json;
  res.json = function (body: any) {
    const duration = Date.now() - startTime;

    const responseAuditData = {
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
      event: "AUTH_RESPONSE",
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      success: res.statusCode >= 200 && res.statusCode < 300,
      provider: req.get("x-provider"),
      action: req.body?.action,
      // Don't log sensitive data like tokens
      hasData: !!body?.data,
    };

    logger.info("AUDIT: Authentication response sent", responseAuditData);

    return originalJson.call(this, body);
  };

  next();
};

/**
 * Log security events for monitoring
 */
export const logSecurityEvent = (
  event: string,
  details: any,
  req?: Request,
  severity: "info" | "warn" | "error" = "info"
) => {
  const securityLog = {
    timestamp: new Date().toISOString(),
    event: `SECURITY_${event}`,
    severity,
    requestId: req?.requestId,
    ip: req?.ip,
    userAgent: req?.get("User-Agent"),
    details,
  };

  logger[severity]("SECURITY EVENT:", securityLog);
};
