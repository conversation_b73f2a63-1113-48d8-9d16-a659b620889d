# =============================================================================
# Docker Environment Configuration
# =============================================================================
# This file contains Docker-specific environment variables
# Copy this to .env and customize for your environment

# Service Ports
AUTH_GATEWAY_PORT=3001
UNIFIED_BACKEND_PORT=8080
MYSQL_PORT=3306
MYSQL_DEV_PORT=3307
KAFKA_PORT=9092
KAFKA_DEV_PORT=9093
KAFKA_UI_PORT=8090
KAFKA_UI_DEV_PORT=8091

# Database Configuration
DATABASE_URL=mysql://zact_user:zact_password@mysql:3306/zact_unified
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_DATABASE=zact_unified
MYSQL_USER=zact_user
MYSQL_PASSWORD=zact_password

# Kafka Configuration
KAFKA_CLIENT_ID=zact-unified-backend

# QBO Configuration (replace with your actual values)
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_REDIRECT_URI=http://localhost:4200/auth/callback
QBO_ENVIRONMENT=sandbox

# Security
JWT_SECRET=your_jwt_secret_key_here
UB_API_KEY=your_unified_backend_api_key
AUTH_GATEWAY_API_KEY=your_auth_gateway_api_key

# API Configuration
API_GATEWAY_URL=http://localhost:8080

# CORS and Security
CORS_ORIGIN=http://localhost:4200
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Feature Flags
INCREMENTAL_SYNC_CRON_ENABLED=false
