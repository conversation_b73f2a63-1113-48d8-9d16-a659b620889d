import { RequestMethod } from "@prisma/client";
import { AxiosError } from "axios";
import { logApiRequest } from "../../utils/syncUtils";
import { v4 as uuidv4 } from "uuid";
import ApiException from "../../utils/api-exception";
import { axiosInstance } from "../../utils/api-utils";
import { logger } from "../../utils/logger";
import { ErrorCode, HttpStatus } from "../../utils/response";
import { envConfig } from "../../config/config";

/**
 * Enhanced API gateway call with better error classification
 */
export const callApiGateway = async (
  service: string,
  entity: string,
  accessToken: string,
  companyId: string,
  connectionId: string,
  options: {
    startPosition?: number;
    maxResults?: number;
    totalCount?: boolean;
    dateField?: string;
    startDate?: string;
  } = {}
) => {
  const requestId = uuidv4();
  const params = new URLSearchParams({
    service,
    entity,
    ...(options.startPosition !== undefined && {
      startPosition: options.startPosition.toString(),
    }),
    ...(options.maxResults !== undefined && {
      maxResults: options.maxResults.toString(),
    }),
    ...(options.totalCount && { totalCount: "true" }),
    ...(options.dateField && { dateField: options.dateField }),
    ...(options.startDate && { startDate: options.startDate }),
  });

  const url = `${envConfig.apiGateway.url}/api/${entity}?${params}`;

  try {
    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.GET,
      { headers: { accessToken: accessToken, companyId } },
      HttpStatus.OK,
      "Calling API Gateway"
    );

    const response = await axiosInstance.get(url, {
      headers: {
        accessToken,
        companyId,
      },
    });

    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.GET,
      { status: response.status, statusText: response.statusText },
      response.status,
      response.data.message || "API call successful"
    );

    return response.data;
  } catch (error) {
    const axiosError: any = error as AxiosError;
    const status =
      axiosError.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
    const axiosErrorCode = axiosError.code;
    const rawErrorData: any = axiosError.response?.data;
    let errorCode = rawErrorData?.error?.status || ErrorCode.INTERNAL_ERROR;
    let errorDescription = `Failed to fetch data from ${service} for entity ${entity}`;

    // Handle ECONNREFUSED
    if (
      axiosErrorCode === "ECONNREFUSED" ||
      axiosError.cause?.code === "ECONNREFUSED" ||
      (axiosError as any)?.errors?.some(
        (err: any) => err.code === "ECONNREFUSED"
      )
    ) {
      errorCode = ErrorCode.CONNECTION_REFUSED;
      errorDescription = `Connection refused while trying to reach ${axiosError.config?.url}`;
    }

    // Handle structured error - try multiple possible error structures
    else if (rawErrorData?.error) {
      const structuredError = rawErrorData.error;
      errorCode =
        structuredError.code ??
        structuredError.responseStatus ??
        ErrorCode.INTERNAL_ERROR;
      errorDescription =
        structuredError.errorDescription ??
        structuredError.message ??
        rawErrorData.message ??
        errorDescription;
    }
    // Handle direct error fields in response
    else if (rawErrorData?.code || rawErrorData?.errorDescription) {
      errorCode = rawErrorData.code ?? ErrorCode.INTERNAL_ERROR;
      errorDescription =
        rawErrorData.errorDescription ??
        rawErrorData.message ??
        errorDescription;
    }
    // Handle response with top-level message and nested error
    else if (rawErrorData?.message && rawErrorData?.error) {
      errorCode = rawErrorData.error.code ?? ErrorCode.INTERNAL_ERROR;
      errorDescription =
        rawErrorData.error.errorDescription ??
        rawErrorData.message ??
        errorDescription;
    }

    logger.error(`External API call failed: ${axiosError.message}`, {
      url,
      status,
      errorData: rawErrorData,
    });

    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.GET,
      {
        error: axiosError.message || errorDescription,
        status,
        errorData: rawErrorData,
      },
      status,
      "API call failed"
    );

    throw new ApiException({
      status,
      code: errorCode,
      errorDescription,
      message: rawErrorData?.message || "", // Capture top-level message
    });
  }
};

/**
 * Fetch all data using planned approach with total count first
 */
export const fetchAllDataWithPlanning = async (
  service: string,
  entity: string,
  accessToken: string,
  companyId: string,
  connectionId: string,
  maxResults: number = 1000,
  options: {
    dateField?: string;
    startDate?: string;
    totalCount?: boolean;
  } = {}
) => {
  // Step 1: Get total count first
  logger.info(`Getting total count for ${entity}...`);

  // Prepare options for total count request
  const totalCountOptions = {
    totalCount: true,
    ...(options.dateField && { dateField: options.dateField }),
    ...(options.startDate && { startDate: options.startDate }),
  };
  logger.info(`Getting options datefield and start date for ${entity}`, {
    dateField: options.dateField,
    startDate: options.startDate,
  });

  const totalCountResponse = await callApiGateway(
    service,
    entity,
    accessToken,
    companyId,
    connectionId,
    totalCountOptions
  );

  // Handle case where no records are found
  const totalCount = totalCountResponse.data?.totalCount || 0;

  // If no records found, return early
  if (totalCount === 0) {
    logger.info(`No records found for ${entity}`);
    return {
      data: [],
      totalCount: 0,
      fetchedCount: 0,
      apiCallsMade: 0,
    };
  }

  // Step 2: Calculate number of API calls needed
  const totalApiCalls = Math.ceil(totalCount / maxResults);

  logger.info("Sync plan created:", {
    entity,
    totalRecords: totalCount,
    maxResults,
    totalApiCalls,
    estimatedTime: `~${(totalApiCalls * 0.5).toFixed(1)} seconds`, // assuming 500ms per call
  });

  // Step 3: Fetch all data using while loop
  const allData: any[] = [];
  let currentCall = 0;
  let startPosition = 1; // QuickBooks uses 1-based indexing

  while (currentCall < totalApiCalls) {
    const endPosition = Math.min(startPosition + maxResults - 1, totalCount);
    logger.info(
      `API Call ${
        currentCall + 1
      }/${totalApiCalls} - Fetching records ${startPosition} to ${endPosition}`
    );

    // Prepare options for data request
    const dataRequestOptions = {
      startPosition,
      maxResults,
      ...(options.dateField && { dateField: options.dateField }),
      ...(options.startDate && { startDate: options.startDate }),
    };

    const response = await callApiGateway(
      service,
      entity,
      accessToken,
      companyId,
      connectionId,
      dataRequestOptions
    );

    // Handle response - assuming response.data contains the entity array
    // e.g., response.data.Account for accounts, response.data.Customer for customers
    const entityKey = entity.charAt(0).toUpperCase() + entity.slice(1); // capitalize first letter
    const pageData = response.data[entityKey] || response.data || [];

    // Ensure pageData is an array
    const dataArray = Array.isArray(pageData) ? pageData : [pageData];
    allData.push(...dataArray);

    // Update counters
    currentCall++;
    startPosition += maxResults;

    // Progress logging
    const progress = ((currentCall / totalApiCalls) * 100).toFixed(1);
    logger.info(
      `Progress: ${allData.length}/${totalCount} records fetched (${progress}%)`
    );

    // Optional: Add delay between calls to avoid rate limiting
    if (currentCall < totalApiCalls) {
      await new Promise((resolve) => setTimeout(resolve, 100)); // 100ms delay
    }
  }

  // Step 4: Validation
  logger.info("Data fetch completed:", {
    expectedRecords: totalCount,
    actualRecords: allData.length,
    isComplete: allData.length === totalCount,
  });

  if (allData.length !== totalCount) {
    logger.warn("Record count mismatch detected", {
      expected: totalCount,
      actual: allData.length,
      difference: totalCount - allData.length,
    });
  }

  return {
    data: allData,
    totalCount,
    fetchedCount: allData.length,
    apiCallsMade: currentCall,
  };
};

/**
 * POST API gateway call for creating entities
 */
export const postApiGateway = async (
  service: string,
  entity: string,
  accessToken: string,
  companyId: string,
  connectionId: string,
  data: any
) => {
  const requestId = uuidv4();
  const params = new URLSearchParams({
    service,
    entity,
  });
  const url = `${envConfig.apiGateway.url}/api/${entity}?${params}`;

  try {
    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.POST,
      { headers: { accessToken: accessToken, companyId }, body: data },
      HttpStatus.OK,
      "Calling API Gateway POST"
    );

    const response = await axiosInstance.post(
      url,
      { data: data },
      {
        headers: {
          accessToken,
          companyId,
          "Content-Type": "application/json",
        },
      }
    );

    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.POST,
      { status: response.status, statusText: response.statusText },
      response.status,
      response.data.message || "API POST call successful"
    );

    return response.data;
  } catch (error) {
    const axiosError: any = error as AxiosError;

    const status =
      axiosError.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
    const axiosErrorCode = axiosError.code;
    const rawErrorData: any = axiosError.response?.data;
    let errorCode = rawErrorData?.error?.status || ErrorCode.INTERNAL_ERROR;
    let errorDescription = `Failed to create ${entity} via ${service}`;

    // Handle ECONNREFUSED
    if (
      axiosErrorCode === "ECONNREFUSED" ||
      axiosError.cause?.code === "ECONNREFUSED" ||
      (axiosError as any)?.errors?.some(
        (err: any) => err.code === "ECONNREFUSED"
      )
    ) {
      errorCode = ErrorCode.CONNECTION_REFUSED;
      errorDescription = `Connection refused while trying to reach ${axiosError.config?.url}`;
    }

    // Handle structured error - try multiple possible error structures
    else if (rawErrorData?.error) {
      const structuredError = rawErrorData.error;
      errorCode =
        structuredError.code ??
        structuredError.responseStatus ??
        ErrorCode.INTERNAL_ERROR;
      errorDescription =
        structuredError.errorDescription ??
        structuredError.message ??
        rawErrorData.message ??
        errorDescription;
    }
    // Handle direct error fields in response
    else if (rawErrorData?.code || rawErrorData?.errorDescription) {
      errorCode = rawErrorData.code ?? ErrorCode.INTERNAL_ERROR;
      errorDescription =
        rawErrorData.errorDescription ??
        rawErrorData.message ??
        errorDescription;
    }
    // Handle response with top-level message and nested error
    else if (rawErrorData?.message && rawErrorData?.error) {
      errorCode = rawErrorData.error.code ?? ErrorCode.INTERNAL_ERROR;
      errorDescription =
        rawErrorData.error.errorDescription ??
        rawErrorData.message ??
        errorDescription;
    }

    logger.error(`External API POST call failed: ${axiosError.message}`, {
      url,
      status,
      errorData: rawErrorData,
    });

    await logApiRequest(
      requestId,
      connectionId,
      url,
      RequestMethod.POST,
      {
        error: axiosError.message || errorDescription,
        status,
        errorData: rawErrorData,
      },
      status,
      "API POST call failed"
    );

    throw new ApiException({
      status,
      code: errorCode,
      errorDescription,
      message: rawErrorData?.message || "", // Capture top-level message
    });
  }
};

// Usage example function
// export const syncEntityData = async (
//   service: string,
//   entity: string,
//   accessToken: string,
//   companyId: string
// ) => {
//   try {
//     logger.info(`Starting sync for ${entity} from ${service}`);

//     const result = await fetchAllDataWithPlanning(
//       service,
//       entity,
//       accessToken,
//       companyId,
//       1000 // QBO maxResults per call
//     );

//     logger.info(`Sync completed successfully:`, {
//       entity,
//       recordsSynced: result.fetchedCount,
//       apiCallsUsed: result.apiCallsMade,
//     });

//     return result.data;
//   } catch (error) {
//     logger.error(`Sync failed for ${entity}:`, error);
//     throw error;
//   }
// };
