import express from "express";
import serviceRoutes from "./serviceRoutes";
import authGatewayRoutes from "./authGatewayRoutes";
import { errorHandler, notFoundHandler } from "../utils/error-handler";
const router = express.Router();

router.use("/auth", authGatewayRoutes);
router.use(serviceRoutes);

// === Error Handling Middleware ===
router.use(notFoundHandler);
router.use(errorHandler);

export default router;
