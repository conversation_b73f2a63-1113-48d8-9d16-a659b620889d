# QBO Disconnection Example

## Overview

This document provides examples of how to test and use the QBO token revocation functionality.

## API Usage Example

### 1. Disconnect QBO Integration

**Request:**

```bash
curl -X POST \
  http://localhost:8080/auth/disconnect \
  -H "Content-Type: application/json" \
  -H "zactcompanyid: your-zact-company-id"
```

**Successful Response:**

```json
{
  "message": "Successfully disconnected from QBO"
}
```

**Error Response (Integration Not Found):**

```json
{
  "status": 404,
  "code": "NOT_FOUND",
  "message": "QBO integration not found for the given organization"
}
```

## What Happens During Disconnection

### 1. Token Revocation Process

```
1. Get latest access and refresh tokens using getValidQboTokenByOrgId
2. Determine accounting platform (qbo/xero) from integration
3. Call platform-specific revoke API to invalidate tokens
4. Update local database to mark integration as inactive
5. Clear stored authentication data
6. Log the disconnection event
```

### 2. Domain Detection Examples

**Sandbox Environment:**

```javascript
// Environment: QBO_ENVIRONMENT=sandbox
// Base URL: https://sandbox-quickbooks.api.intuit.com
// Detected Domain: "sandbox"
// Revoke URL: https://developer.api.intuit.com/v2/oauth2/tokens/revoke
```

**Production Environment:**

```javascript
// Environment: QBO_ENVIRONMENT=production
// Base URL: https://quickbooks.api.intuit.com
// Detected Domain: "production"
// Revoke URL: https://developer.api.intuit.com/v2/oauth2/tokens/revoke
```

**Stored Domain (Highest Priority):**

```javascript
// Integration authentication data contains:
{
  "accessToken": "...",
  "refreshToken": "...",
  "domain": "production"  // This takes precedence
}
// Detected Domain: "production"
```

## Testing Scenarios

### 1. Test Successful Disconnection

**Prerequisites:**

- Active QBO integration exists
- Valid access and refresh tokens
- QBO API is accessible

**Steps:**

1. Connect to QBO (create integration)
2. Verify integration is active in database
3. Call disconnect API
4. Verify tokens are revoked at QBO
5. Verify integration status is INACTIVE
6. Verify authentication data is cleared

### 2. Test Domain Detection

**Sandbox Test:**

```bash
# Set environment to sandbox
export QBO_ENVIRONMENT=sandbox
export QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com

# Connect and then disconnect
curl -X POST http://localhost:8080/auth/disconnect/qbo \
  -H "zactcompanyid: test-company-sandbox"
```

**Production Test:**

```bash
# Set environment to production
export QBO_ENVIRONMENT=production
export QBO_API_BASE_URL=https://quickbooks.api.intuit.com

# Connect and then disconnect
curl -X POST http://localhost:8080/auth/disconnect/qbo \
  -H "zactcompanyid: test-company-production"
```

### 3. Test Error Handling

**Missing Integration:**

```bash
curl -X POST http://localhost:8080/auth/disconnect/qbo \
  -H "zactcompanyid: non-existent-company"
# Expected: 404 Not Found
```

**Missing Header:**

```bash
curl -X POST http://localhost:8080/auth/disconnect/qbo
# Expected: 400 Bad Request - zactcompanyid header required
```

**Unsupported Platform:**

```bash
curl -X POST http://localhost:8080/auth/disconnect/unsupported \
  -H "zactcompanyid: test-company"
# Expected: 422 Unprocessable Entity
```

## Database Verification

### Before Disconnection

```sql
SELECT
  id,
  zact_company_id,
  connection_status,
  authentication
FROM accounting_platform_integration
WHERE zact_company_id = 'your-company-id';

-- Result:
-- connection_status: ACTIVE
-- authentication: {"accessToken": "...", "refreshToken": "...", "domain": "sandbox"}
```

### After Disconnection

```sql
SELECT
  id,
  zact_company_id,
  connection_status,
  authentication
FROM accounting_platform_integration
WHERE zact_company_id = 'your-company-id';

-- Result:
-- connection_status: INACTIVE
-- authentication: {"accessToken": null, "refreshToken": null, "expiresAt": null, "lastRefreshedAt": null}
```

## Log Analysis

### Successful Disconnection Logs

```
INFO: Revoking QBO tokens for integration abc-123 {
  "integrationId": "abc-123",
  "orgId": "company-456",
  "domain": "sandbox",
  "companyName": "Test Company"
}

INFO: Successfully revoked QBO tokens {
  "domain": "sandbox",
  "revokeUrl": "https://developer.api.intuit.com/v2/oauth2/tokens/revoke",
  "status": 200
}

INFO: Successfully disconnected QBO integration for organization company-456 {
  "integrationId": "abc-123",
  "orgId": "company-456"
}
```

### Error Handling Logs

```
WARN: No valid tokens found for integration abc-123, skipping token revocation {
  "integrationId": "abc-123",
  "orgId": "company-456",
  "hasAccessToken": false,
  "hasRefreshToken": false
}

ERROR: Failed to revoke QBO tokens {
  "error": "Request failed with status code 400",
  "domain": "sandbox",
  "status": 400,
  "statusText": "Bad Request",
  "data": {"error": "invalid_token"}
}
```

## Integration with Frontend

### JavaScript Example

```javascript
async function disconnectQBO(zactCompanyId) {
  try {
    const response = await fetch("/auth/disconnect/qbo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        zactcompanyid: zactCompanyId,
      },
    });

    if (response.ok) {
      const result = await response.json();
      console.log("Disconnected successfully:", result.message);
      // Update UI to reflect disconnection
      updateConnectionStatus("disconnected");
    } else {
      const error = await response.json();
      console.error("Disconnection failed:", error.message);
      // Show error message to user
      showErrorMessage(error.message);
    }
  } catch (error) {
    console.error("Network error:", error);
    showErrorMessage("Failed to disconnect. Please try again.");
  }
}
```

### React Component Example

```jsx
const QBOConnectionManager = ({ zactCompanyId }) => {
  const [isConnected, setIsConnected] = useState(true);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  const handleDisconnect = async () => {
    setIsDisconnecting(true);
    try {
      await disconnectQBO(zactCompanyId);
      setIsConnected(false);
    } catch (error) {
      // Error handling
    } finally {
      setIsDisconnecting(false);
    }
  };

  return (
    <div>
      {isConnected ? (
        <button onClick={handleDisconnect} disabled={isDisconnecting}>
          {isDisconnecting ? "Disconnecting..." : "Disconnect QBO"}
        </button>
      ) : (
        <span>Not connected to QBO</span>
      )}
    </div>
  );
};
```

## Security Considerations

1. **Token Cleanup**: Tokens are immediately cleared from database
2. **Error Information**: Sensitive data is not exposed in error responses
3. **Audit Trail**: All disconnection events are logged
4. **Graceful Degradation**: Local disconnection succeeds even if QBO API fails

## Troubleshooting

### Common Issues

1. **QBO API Timeout**: Increase timeout in axios configuration
2. **Invalid Credentials**: Verify QBO_CLIENT_ID and QBO_CLIENT_SECRET
3. **Network Issues**: Check connectivity to QBO API endpoints
4. **Token Already Revoked**: This is handled gracefully and logged as warning
