import logger from "../utils/logger";

/**
 * Validate payment request payload
 */
export const validatePaymentRequest = (
  payload: any
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  logger.info("payload received for validation:", payload);
  
  if (!payload.vendorId) errors.push("vendorId is required");
  if (!payload.paymentType) errors.push("paymentType is required");
  if (!["Check", "CreditCard", "Cash"].includes(payload.paymentType)) {
    errors.push("paymentType must be one of: Check, CreditCard, Cash");
  }
  if (!payload.totalAmount || payload.totalAmount <= 0) {
    errors.push("totalAmount is required and must be greater than 0");
  }
  if (!payload.paymentDate) errors.push("paymentDate is required");
  if (!payload.billPayments || !Array.isArray(payload.billPayments) || payload.billPayments.length === 0) {
    errors.push("billPayments is required and must be a non-empty array");
  }
  
  // Validate bill payments array
  if (payload.billPayments && Array.isArray(payload.billPayments)) {
    payload.billPayments.forEach((payment: any, index: number) => {
      if (!payment.billId) {
        errors.push(`billPayments[${index}].billId is required`);
      }
      if (!payment.amount || payment.amount <= 0) {
        errors.push(`billPayments[${index}].amount is required and must be greater than 0`);
      }
    });
  }

  // Validate payment method specific fields
  if (payload.paymentType === "Check" && !payload.bankAccountId) {
    errors.push("bankAccountId is required for Check payments");
  }
  if (payload.paymentType === "CreditCard" && !payload.creditCardAccountId) {
    errors.push("creditCardAccountId is required for CreditCard payments");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
