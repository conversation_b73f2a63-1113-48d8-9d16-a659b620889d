import baseProducer from "./base-producer";
import logger from "../../utils/logger";

// Topic constants
export const TOPICS = {
  STV_CONN_SYNC_COMPLETION: "unified-backend-sync-completed",
  SYNC_FAILED: "unified-backend-sync-failed",
  CONNECTION_STATUS_CHANGED: "unified-backend-connection-status-changed",
};

/**
 * Send a sync completed event
 * @param connectionId The accounting platform connection ID
 * @param entityType The type of entity that was synced
 * @param stats Sync statistics
 */
export const sendSyncCompletedEvent = async (
  connectionId: string,
  entityType: string,
  stats: {
    recordsProcessed: number;
    recordsSucceeded: number;
    recordsFailed: number;
    syncStartedAt: Date;
    syncCompletedAt: Date;
  }
): Promise<void> => {
  try {
    await baseProducer.sendMessage(
      TOPICS.STV_CONN_SYNC_COMPLETION,
      {
        connectionId,
        entityType,
        stats,
        timestamp: new Date().toISOString(),
      },
      connectionId
    );
    logger.info(
      `Sync completed event sent for ${entityType} on connection ${connectionId}`
    );
  } catch (error) {
    logger.error(`Failed to send sync completed event: ${error}`);
  }
};

/**
 * Send a sync failed event
 * @param connectionId The accounting platform connection ID
 * @param entityType The type of entity that failed to sync
 * @param error Error details
 */
export const sendSyncFailedEvent = async (
  connectionId: string,
  entityType: string,
  error: {
    message: string;
    code?: string;
    details?: any;
  }
): Promise<void> => {
  try {
    await baseProducer.sendMessage(
      TOPICS.SYNC_FAILED,
      {
        connectionId,
        entityType,
        error,
        timestamp: new Date().toISOString(),
      },
      connectionId
    );
    logger.info(
      `Sync failed event sent for ${entityType} on connection ${connectionId}`
    );
  } catch (err) {
    logger.error(`Failed to send sync failed event: ${err}`);
  }
};

/**
 * Send a connection status changed event
 * @param connectionId The accounting platform connection ID
 * @param status The new connection status
 * @param details Additional details about the status change
 */
export const sendConnectionStatusChangedEvent = async (
  connectionId: string,
  status: string,
  details?: any
): Promise<void> => {
  try {
    await baseProducer.sendMessage(
      TOPICS.CONNECTION_STATUS_CHANGED,
      {
        connectionId,
        status,
        details,
        timestamp: new Date().toISOString(),
      },
      connectionId
    );
    logger.info(
      `Connection status changed event sent for connection ${connectionId}: ${status}`
    );
  } catch (error) {
    logger.error(`Failed to send connection status changed event: ${error}`);
  }
};
