import prisma from "../config/db";
import ApiException from "../utils/api-exception";
import { ErrorCode, HttpStatus } from "../utils/response";
import logger from "../utils/logger";
import { authGatewayClient } from "../services/authGateway/authGatewayClient";

/**
 * Function to validate QBO access token
 * This function:
 * 1. Retrieves the token for the given integration ID
 * 2. Checks if the token is valid and not expired
 * 3. Returns the access token and realm ID
 *
 * Note: Token refresh is now handled by the Auth Gateway microservice
 *
 * @param connectionId The accounting Platform integration ID
 * @returns Object containing accessToken and realmId
 */
export const getValidQboToken = async (
  connectionId: string
): Promise<{ accessToken: string; realmId: string }> => {
  try {
    // Validate the integration ID
    if (!connectionId) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        message: "Integration ID is required",
      });
    }

    // Get the accounting Platform integration
    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: {
        id: connectionId,
      },
    });

    // If no integration found, throw an error
    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: `No accounting Platform integration found with ID: ${connectionId}`,
      });
    }

    // If integration is not active, throw an error
    if (integration.connectionStatus !== "ACTIVE") {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        message: `Integration is not active. Current status: ${integration.connectionStatus}`,
      });
    }

    // Get the realm ID (external company ID)
    const realmId = integration.externalCompanyId;

    // Get the authentication tokens from the integration
    const auth = integration.authentication as any;

    // If no authentication found, throw an error
    if (!auth || !auth.accessToken || !auth.refreshToken || !auth.expiresAt) {
      throw new ApiException({
        status: HttpStatus.UNAUTHORIZED,
        code: ErrorCode.UNAUTHORIZED,
        message: "QBO authentication tokens not found",
      });
    }

    // Check if token is expired
    if (isTokenExpired(auth.expiresAt)) {
      logger.warn(
        `Token expired for integration ${connectionId}. Attempting token refresh via Auth Gateway.`
      );

      try {
        // Attempt to refresh tokens via Auth Gateway
        await authGatewayClient.refreshTokens(connectionId);

        // Re-fetch the updated integration data
        const updatedIntegration =
          await prisma.accountingPlatformIntegration.findUnique({
            where: { id: connectionId },
          });

        if (!updatedIntegration || !updatedIntegration.authentication) {
          throw new ApiException({
            status: HttpStatus.INTERNAL_SERVER_ERROR,
            code: ErrorCode.INTERNAL_ERROR,
            message:
              "Failed to retrieve updated authentication data after refresh",
          });
        }

        const updatedAuth = updatedIntegration.authentication as any;

        if (!updatedAuth.accessToken) {
          throw new ApiException({
            status: HttpStatus.INTERNAL_SERVER_ERROR,
            code: ErrorCode.INTERNAL_ERROR,
            message: "Updated authentication data is incomplete",
          });
        }

        logger.info(
          `Successfully refreshed tokens for integration ${connectionId}`
        );

        // Return the refreshed token
        return {
          accessToken: updatedAuth.accessToken,
          realmId,
        };
      } catch (refreshError) {
        logger.error(
          `Failed to refresh tokens for integration ${connectionId}:`,
          refreshError
        );

        // If refresh fails, throw the original error or the refresh error
        if (refreshError instanceof ApiException) {
          throw refreshError;
        }

        throw new ApiException({
          status: HttpStatus.UNAUTHORIZED,
          code: ErrorCode.UNAUTHORIZED,
          message:
            "QBO authentication token has expired and refresh failed. Please re-authenticate through Auth Gateway.",
        });
      }
    }

    // Token is valid, return it
    return {
      accessToken: auth.accessToken,
      realmId,
    };
  } catch (error) {
    // If it's already an ApiException, just rethrow it
    if (error instanceof ApiException) {
      throw error;
    }

    // For any other type of error
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: `Error validating QBO token: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    });
  }
};

/**
 * Helper function to check if a token is expired
 * @param expiresAt The expiration timestamp
 * @returns true if token is expired, false otherwise
 */
function isTokenExpired(expiresAt: string): boolean {
  const now = new Date();
  const expiration = new Date(expiresAt);
  // Add 5 minute buffer to account for clock skew
  const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
  return now.getTime() >= expiration.getTime() - bufferTime;
}

/**
 * Function to get a valid QBO token by organization ID
 * This function:
 * 1. Finds the active accounting Platform integration for the given organization ID
 * 2. Gets a valid token for that integration
 *
 * @param orgId The organization ID (zactCompanyId)
 * @returns Object containing accessToken and realmId
 */
export const getValidQboTokenByOrgId = async (
  orgId: string
): Promise<{ accessToken: string; realmId: string }> => {
  // Validate the organization ID
  if (!orgId) {
    throw new ApiException({
      status: HttpStatus.BAD_REQUEST,
      code: ErrorCode.BAD_REQUEST,
      message: "Organization ID is required",
    });
  }

  // Find the active accounting Platform integration for this organization
  const integration = await prisma.accountingPlatformIntegration.findFirst({
    where: {
      zactCompanyId: orgId,
      connectionStatus: "ACTIVE",
    },
  });

  // If no integration found, throw an error
  if (!integration) {
    throw new ApiException({
      status: HttpStatus.NOT_FOUND,
      code: ErrorCode.NOT_FOUND,
      message:
        "No active accounting Platform integration found for this organization",
    });
  }

  // Get a valid token for this integration
  // This will throw ApiException if an error occurs, which will be caught by the global error handler
  return await getValidQboToken(integration.id);
};
