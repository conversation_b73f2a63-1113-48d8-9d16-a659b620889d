export interface IQboVendorResponse {
  Id: string;
  DisplayName: string;
  PrimaryEmailAddr?: {
    Address: string;
  };
  PrimaryPhone?: {
    FreeFormNumber: string;
  };
  WebAddr?: {
    URI: string;
  };
  AcctNum?: string;
  BillAddr?: {
    Line1?: string;
    City?: string;
    PostalCode?: string;
    CountrySubDivisionCode?: string;
    Country?: string;
  };
  Active: boolean;
  Balance: number;
  PrintOnCheckName?: string;
  Vendor1099?: boolean;
  TaxIdentifier?: string;
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
  domain: string;
}

export interface IUnifiedVendor {
  id: string;
  vendorName: string; // MANDATORY - Required by ALL platforms
  contactName: string | null; // Changed to allow null
  email: string | null;
  phone: string | null;
  website: string | null;
  bankAccountNumber: string | null;
  addresses: Array<{
    address1: string | null;
    city: string | null;
    postalCode: string | null;
    region: string | null;
    country: string | null;
  }> | null; // Changed to allow null like in Prisma
  isActive: boolean; // MANDATORY - Required by ALL platforms
  balance: number | null; // Changed to allow null
  taxNumber: string | null;
  // Optional fields - Platform specific requirements
  currencyCode?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  paymentTermsId?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  defaultExpenseAccountId?: string | null; // Required by: QBO, NetSuite only
  defaultBillPaymentAccountId?: string | null; // Required by: QBO, NetSuite only
  vendorNumber?: string | null; // Required by: QBO, QBD, NetSuite (not Xero, Merge, Rutter)
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  createdAtPlatform: Date;
  updatedAtPlatform: Date | null; // Changed to allow null
  domain: string;
}
