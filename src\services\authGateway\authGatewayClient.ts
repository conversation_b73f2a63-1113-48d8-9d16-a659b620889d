import axios from "axios";
import { envConfig } from "../../config/config";
import logger from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { HttpStatus, ErrorCode } from "../../utils/response";

/**
 * Client for communicating with Auth Gateway
 */
export class AuthGatewayClient {
  private baseUrl: string;
  private apiKey: string | undefined;
  private timeout: number;

  constructor() {
    this.baseUrl = envConfig.authGateway.baseUrl;
    this.apiKey = envConfig.authGateway.apiKey;
    this.timeout = envConfig.authGateway.timeout;
  }

  /**
   * Refresh tokens via Auth Gateway
   */
  async refreshTokens(connectionId: string): Promise<{
    message: string;
    data: {
      connectionId: string;
      realmId: string;
      erpSystem: string;
    };
  }> {
    try {
      logger.info(`Requesting token refresh from Auth Gateway for connectionId: ${connectionId}`);

      const response = await axios.post(
        `${this.baseUrl}/auth`,
        {
          action: "refresh",
          connectionId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-provider": "qbo",
            ...(this.apiKey && { "Authorization": `Bearer ${this.apiKey}` }),
          },
          timeout: this.timeout,
        }
      );

      if (!response.data || !response.data.data) {
        throw new ApiException({
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: "Invalid response from Auth Gateway",
        });
      }

      logger.info(`Successfully refreshed tokens via Auth Gateway for connectionId: ${connectionId}`);

      return response.data;
    } catch (error: any) {
      logger.error("Error refreshing tokens via Auth Gateway:", error);

      if (error instanceof ApiException) {
        throw error;
      }

      // Handle axios errors
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        // If Auth Gateway returns 401, it means the refresh token is also expired
        if (status === 401) {
          throw new ApiException({
            status: HttpStatus.UNAUTHORIZED,
            code: ErrorCode.UNAUTHORIZED,
            message: "Refresh token has expired. Please re-authenticate through Auth Gateway.",
          });
        }

        throw new ApiException({
          status: status || HttpStatus.INTERNAL_SERVER_ERROR,
          code: ErrorCode.INTERNAL_ERROR,
          message: `Auth Gateway error: ${message}`,
        });
      }

      throw new ApiException({
        status: HttpStatus.SERVICE_UNAVAILABLE,
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: "Unable to communicate with Auth Gateway",
      });
    }
  }
}

export const authGatewayClient = new AuthGatewayClient();
