# Development Dockerfile for Unified Backend
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for native modules
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./
COPY prisma/schema.prisma ./prisma/

# Install dependencies (including dev dependencies)
RUN npm ci && \
    npx prisma generate

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port and debug port
EXPOSE 8080 9229

# Start with nodemon for hot reload and debugging
CMD ["npm", "run", "dev"]
