version: '3.8'

services:
  # Auth Gateway Development Service
  auth-gateway-dev:
    build:
      context: ./auth-gateway
      dockerfile: Dockerfile.dev
    container_name: zact-auth-gateway-dev
    ports:
      - "${AUTH_GATEWAY_PORT:-3001}:3001"
      - "9230:9229"  # Debug port for Auth Gateway
    environment:
      - NODE_ENV=development
      - PORT=3001
      - UB_BASE_URL=http://unified-backend-dev:8080
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - QBO_REDIRECT_URI=${QBO_REDIRECT_URI}
      - QBO_ENVIRONMENT=${QBO_ENVIRONMENT:-sandbox}
      - UB_API_KEY=${UB_API_KEY}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:4200}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-1000}
      - LOG_LEVEL=${LOG_LEVEL:-debug}
    volumes:
      - ./auth-gateway:/app
      - /app/node_modules
      - ./auth-gateway/logs:/app/logs
    depends_on:
      - unified-backend-dev
    networks:
      - zact-dev-network
    command: npm run dev

  # Unified Backend Development Service
  unified-backend-dev:
    build:
      context: ./unified_backend
      dockerfile: Dockerfile.dev
    container_name: zact-unified-backend-dev
    ports:
      - "${UNIFIED_BACKEND_PORT:-8080}:8080"
      - "9229:9229"  # Debug port for Unified Backend
    environment:
      - NODE_ENV=development
      - PORT=8080
      - DATABASE_URL=mysql://zact_dev:devpassword@mysql-dev:3306/zact_unified_dev
      - KAFKA_BROKERS=kafka-dev:29092
      - KAFKA_CLIENT_ID=zact-unified-backend-dev
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - API_GATEWAY_URL=${API_GATEWAY_URL}
      - JWT_SECRET=${JWT_SECRET}
      - INCREMENTAL_SYNC_CRON_ENABLED=false
      - AUTH_GATEWAY_URL=http://auth-gateway-dev:3001
      - AUTH_GATEWAY_API_KEY=${AUTH_GATEWAY_API_KEY}
    volumes:
      - ./unified_backend:/app
      - /app/node_modules
      - ./unified_backend/logs:/app/logs
    depends_on:
      mysql-dev:
        condition: service_healthy
      kafka-dev:
        condition: service_healthy
    networks:
      - zact-dev-network
    command: npm run dev

  # MySQL Development Database
  mysql-dev:
    image: mysql:8.0
    container_name: zact-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: devpassword
      MYSQL_DATABASE: zact_unified_dev
      MYSQL_USER: zact_dev
      MYSQL_PASSWORD: devpassword
    ports:
      - "${MYSQL_DEV_PORT:-3307}:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./infrastructure/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - zact-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pdevpassword"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Zookeeper Development
  zookeeper-dev:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zact-zookeeper-dev
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - zookeeper_dev_data:/var/lib/zookeeper/data
      - zookeeper_dev_logs:/var/lib/zookeeper/log
    networks:
      - zact-dev-network

  # Kafka Development
  kafka-dev:
    image: confluentinc/cp-kafka:7.4.0
    container_name: zact-kafka-dev
    restart: unless-stopped
    depends_on:
      - zookeeper-dev
    ports:
      - "${KAFKA_DEV_PORT:-9093}:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-dev:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-dev:29092,PLAINTEXT_HOST://localhost:9093
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 24
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_MESSAGE_MAX_BYTES: 1000000
    volumes:
      - kafka_dev_data:/var/lib/kafka/data
    networks:
      - zact-dev-network
    healthcheck:
      test: ["CMD-SHELL", "kafka-broker-api-versions --bootstrap-server localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka UI Development
   

# Networks
networks:
  zact-dev-network:
    driver: bridge
    name: zact-dev-network

# Volumes
volumes:
  mysql_dev_data:
    name: zact-mysql-dev-data
  kafka_dev_data:
    name: zact-kafka-dev-data
  zookeeper_dev_data:
    name: zact-zookeeper-dev-data
  zookeeper_dev_logs:
    name: zact-zookeeper-dev-logs
