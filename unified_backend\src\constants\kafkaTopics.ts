/**
 * Kafka topic constants
 */
export const K<PERSON><PERSON>_TOPICS = {
  // Entity operations
  ENTITY_CREATE_REQUEST: "entity-create-request",
  ENTITY_CREATE_RESPONSE: "entity-create-response",
  ENTITY_BATCH_STREAM: "entity-batch-stream",

  // Sync operations
  SYNC_COMPLETION_RESPONSE: "sync-info",

  // Error handling
  SYNC_ERROR_TOPIC: "sync-info",
} as const;

/**
 * Legacy topic constants for backward compatibility
 * @deprecated Use KAFKA_TOPICS instead
 */
export const TOPICS = {
  ENTITY_CREATE_REQUEST: KAFKA_TOPICS.ENTITY_CREATE_REQUEST,
  ENTITY_CREATE_RESPONSE: KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
} as const;
