export interface IQboBillResponse {
  Id: string;
  domain: string;
  APAccountRef?: {
    value: string;
  };
  VendorRef?: {
    value: string;
  };
  TxnDate?: string;
  DueDate?: string;
  TotalAmt: number;
  CurrencyRef?: {
    value: string;
  };
  SalesTermRef?: {
    value: string;
  };
  Balance: number;
  Line: Array<{
    Description?: string;
    Amount: number;
    DetailType: string;
    AccountBasedExpenseLineDetail?: {
      AccountRef?: {
        value: string;
      };
      TaxCodeRef?: {
        value: string;
      };
      CustomerRef?: {
        value: string;
      };
    };
    ProjectRef?: {
      value: string;
    };
  }>;
  LinkedTxn?: Array<{
    TxnId: string;
    TxnType?: string;
  }>;
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
}

export interface IUnifiedBill {
  id: string;
  vendorId: string; // MANDATORY - Required by ALL platforms
  externalBillId?: string | null;
  billNumber: string; // MANDATORY - Required by ALL platforms
  billDate: string; // MANDATORY - Required by ALL platforms (ISO date string)
  dueDate: string; // MANDATORY - Required by ALL platforms (ISO date string)
  totalAmount: number; // MANDATORY - Required by ALL platforms
  balance: number; // MANDATORY - Required by ALL platforms
  currency: string; // MANDATORY - Required by ALL platforms
  status: string; // MANDATORY - Required by ALL platforms
  privateNote?: string | null;
  exchangeRate?: number | null;
  // Optional fields - Platform specific requirements
  subTotal?: number | null; // Required by: Xero, NetSuite, Merge, Rutter (not QBO, QBD)
  taxAmount?: number | null; // Required by: Xero, NetSuite, Merge, Rutter (not QBO, QBD)
  discountAmount?: number | null; // Required by: Xero, NetSuite, Merge, Rutter (not QBO, QBD)
  paymentTermsId?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  referenceNumber?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  lineItems: Array<{
    description: string | null;
    amount: number; // MANDATORY - Required by ALL platforms
    detailType: string;
    accountId: string | null;
    taxRateId: string | null;
    customerId: string | null;
    projectId: string | null;
    // Optional line item fields - Platform specific requirements
    classId?: string | null; // Required by: QBO, QBD only (for tracking categories)
    departmentId?: string | null; // Required by: NetSuite only
    locationId?: string | null; // Required by: NetSuite only
    employeeId?: string | null; // Required by: NetSuite, Merge only
    unitPrice?: number | null; // Required by: QBO, Xero, NetSuite (not QBD, Merge, Rutter)
    quantity?: number | null; // Required by: QBO, Xero, NetSuite (not QBD, Merge, Rutter)
    itemId?: string | null; // Required by: QBO, Xero, NetSuite (not QBD, Merge, Rutter)
  }>;
  accountId?: string | null;
  classId?: string | null;
  createdAtPlatform: Date;
  updatedAtPlatform: Date | null;
  domain: string;
}

export interface IQboPaymentResponse {
  Id: string;
  domain: string;
  VendorRef?: {
    value: string;
  };
  PayType: "Check" | "CreditCard" | "Cash";
  TotalAmt: number;
  TxnDate?: string;
  CurrencyRef?: {
    value: string;
  };
  CheckPayment?: {
    BankAccountRef?: {
      value: string;
    };
    PrintStatus?: string;
  };
  CreditCardPayment?: {
    CCAccountRef?: {
      value: string;
    };
  };
  Line: Array<{
    Amount: number;
    LinkedTxn?: Array<{
      TxnId: string;
      TxnType: string;
    }>;
  }>;
  PrivateNote?: string;
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
}

export interface IUnifiedPayment {
  id: string;
  vendorId: string; // MANDATORY - Required by ALL platforms
  externalPaymentId?: string | null;
  paymentType: "Check" | "CreditCard" | "Cash"; // MANDATORY - Required by ALL platforms
  totalAmount: number; // MANDATORY - Required by ALL platforms
  paymentDate: string; // MANDATORY - Required by ALL platforms (ISO date string)
  currency: string; // MANDATORY - Required by ALL platforms
  bankAccountId?: string | null;
  creditCardAccountId?: string | null;
  checkNumber?: string | null;
  privateNote?: string | null;
  // Optional fields - Platform specific requirements
  referenceNumber?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  exchangeRate?: number | null; // Required by: Xero, NetSuite, Merge, Rutter (not QBO, QBD)
  paymentMethodId?: string | null; // Required by: QBO, Xero, NetSuite, Merge, Rutter (not QBD)
  memo?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  billPayments: Array<{
    billId: string; // MANDATORY - Required by ALL platforms
    amount: number; // MANDATORY - Required by ALL platforms
    // Optional bill payment fields - Platform specific requirements
    discountAmount?: number | null; // Required by: QBO, Xero, NetSuite (not QBD, Merge, Rutter)
    appliedDate?: string | null; // Required by: QBO, Xero, NetSuite (not QBD, Merge, Rutter)
  }>;
  createdAtPlatform: Date;
  updatedAtPlatform: Date | null;
  domain: string;
}
