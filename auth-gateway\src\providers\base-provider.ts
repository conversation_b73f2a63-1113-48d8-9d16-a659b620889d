import { AuthProvider } from "../interfaces/provider";
import {
  AuthUrlResponse,
  TokenResponse,
  RevokeResponse,
} from "../interfaces/auth";

/**
 * Abstract base class for all authentication providers
 * Provides common functionality and enforces interface implementation
 */
export abstract class BaseAuthProvider implements AuthProvider {
  protected providerName: string;

  constructor(providerName: string) {
    this.providerName = providerName;
  }

  /**
   * Get provider name
   */
  getProviderName(): string {
    return this.providerName;
  }

  /**
   * Abstract methods that must be implemented by concrete providers
   */
  abstract generateAuthUrl(orgId: string): Promise<AuthUrlResponse>;

  abstract exchangeCodeForToken(
    code: string,
    realmId: string,
    state: string
  ): Promise<TokenResponse>;

  abstract refreshToken(connectionId: string): Promise<TokenResponse>;

  abstract revokeToken(connectionId: string): Promise<RevokeResponse>;

  /**
   * Common validation helper
   */
  protected validateRequired(value: any, fieldName: string): void {
    if (!value) {
      throw new Error(
        `${fieldName} is required for ${this.providerName} provider`
      );
    }
  }

  /**
   * Common error handling helper
   */
  protected handleProviderError(error: any, operation: string): never {
    const message = `${this.providerName} ${operation} failed: ${
      error.message || error
    }`;
    throw new Error(message);
  }
}
