#!/bin/bash

# Development Workflow Helper Scripts

case "$1" in
  "start")
    echo "🚀 Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "✅ Services started!"
    echo "📊 Auth Gateway: http://localhost:3001"
    echo "📊 Unified Backend: http://localhost:8080"
    echo "📊 Kafka UI: http://localhost:8091"
    ;;
    
  "stop")
    echo "🛑 Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Services stopped!"
    ;;
    
  "restart-auth")
    echo "🔄 Restarting Auth Gateway..."
    docker-compose -f docker-compose.dev.yml restart auth-gateway-dev
    echo "✅ Auth Gateway restarted!"
    ;;
    
  "restart-backend")
    echo "🔄 Restarting Unified Backend..."
    docker-compose -f docker-compose.dev.yml restart unified-backend-dev
    echo "✅ Unified Backend restarted!"
    ;;
    
  "logs")
    if [ -z "$2" ]; then
      echo "📋 Showing all logs..."
      docker-compose -f docker-compose.dev.yml logs -f
    else
      echo "📋 Showing logs for $2..."
      docker-compose -f docker-compose.dev.yml logs -f "$2"
    fi
    ;;
    
  "status")
    echo "📊 Service Status:"
    docker-compose -f docker-compose.dev.yml ps
    ;;
    
  "clean")
    echo "🧹 Cleaning up development environment..."
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -f
    echo "✅ Cleanup complete!"
    ;;
    
  *)
    echo "🔧 Development Workflow Helper"
    echo ""
    echo "Usage: $0 {start|stop|restart-auth|restart-backend|logs|status|clean}"
    echo ""
    echo "Commands:"
    echo "  start           - Start all development services"
    echo "  stop            - Stop all development services"
    echo "  restart-auth    - Restart only Auth Gateway"
    echo "  restart-backend - Restart only Unified Backend"
    echo "  logs [service]  - Show logs (all or specific service)"
    echo "  status          - Show service status"
    echo "  clean           - Clean up everything (volumes, images)"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs auth-gateway-dev"
    echo "  $0 restart-auth"
    ;;
esac
