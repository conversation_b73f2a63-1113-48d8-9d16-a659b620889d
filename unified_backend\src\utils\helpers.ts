import ApiException from "./api-exception";
import { ErrorCodes } from "./response";

// src/utils/helpers.ts
export const isInvalidStatus = (status?: number) => {
  return !status || status < 100 || status > 599;
};

interface StateData {
  zactCompanyId: string;
}

export function parseAndValidateState(state: string): StateData {
  const stateData: StateData = JSON.parse(
    Buffer.from(state, "base64").toString("utf8")
  );

  if (!stateData.zactCompanyId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "zactCompanyId not found in state data",
    });
  }

  return stateData;
}

// Method 1: Calculate size from JSON string
export const getMessageSizeInKB = (message: any): number => {
  const jsonString = JSON.stringify(message);
  const sizeInBytes = new TextEncoder().encode(jsonString).length;
  return Math.round((sizeInBytes / 1024) * 100) / 100; // Round to 2 decimal places
};

/**
 * Convert snake_case string to camelCase
 */
export const toCamelCase = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * Convert object keys from snake_case to camelCase recursively
 */
export const convertToCamelCase = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(convertToCamelCase);
  }

  if (typeof obj === "object" && obj.constructor === Object) {
    const converted: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = toCamelCase(key);
      converted[camelKey] = convertToCamelCase(value);
    }
    return converted;
  }

  return obj;
};
